{"globals": {"EffectScope": true, "axiosReq": true, "bus": true, "casHandleChange": true, "cloneDeep": true, "closeElLoading": true, "commonUtil": true, "computed": true, "copyValueToClipboard": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "elConfirm": true, "elConfirmNoCancelBtn": true, "elLoading": true, "elMessage": true, "elNotify": true, "fetchData": true, "filterAsyncRouter": true, "filterAsyncRouterByCodes": true, "filterAsyncRoutesByMenuList": true, "filterAsyncRoutesByRoles": true, "freshRouter": true, "getCurrentInstance": true, "getCurrentScope": true, "getLangInstance": true, "getQueryParam": true, "h": true, "inject": true, "isExternal": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "langTitle": true, "markRaw": true, "markdownConfig": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "progressClose": true, "progressStart": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resetRouter": true, "resetState": true, "resizeHandler": true, "resolveComponent": true, "resolveDirective": true, "routeInfo": true, "routerBack": true, "routerPush": true, "routerReplace": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "sleepTimeout": true, "storeToRefs": true, "toRaw": true, "toRef": true, "toRefs": true, "triggerRef": true, "unref": true, "useAttrs": true, "useBasicStore": true, "useConfigStore": true, "useCssModule": true, "useCssVars": true, "useElement": true, "useErrorLog": true, "useLink": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTable": true, "useTagsViewStore": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}