export default {
  analysisButton: '分析',
  timeRangeTip: '请选择异常发生时间进行分析',
  timeTip: '选择异常发生时间',
  refreshTip: '自动刷新',
  queryTimeTip: '异常时间',
  modelTip: '诊断模型',
  instanceTip: '实例',
  reviewDrawerTitle: '回看分析过程',
  timeStartTip: '开始时间',
  timeEndTip: '结束时间',
  playbackButton: '回放',
  reportButton: '报告',
  setpTitle1: '异常分析',
  playbackAnimationTip: '回放动画',
  animationSpeedTip: '动画速度',
  setpTip1: 'DBA收到异常提醒后，会针对该异常进行分析，进而分配任务给不同的同事，接收到任务的同事会先独立进行分析。',
  setpTitle2: '圆桌讨论',
  setpTip2: '接收任务的同事独立进行异常分析后，会加入群组，进行圆桌讨论。',
  setpTitle3: '报告展示',
  setpTip3: '圆桌讨论后，DBA会将讨论结果汇总，出具异常分析诊断报告。',
  KnowledgeListTitle: '知识库列表',
  IgnoreMessageCache: '缓存有误，点击重新生成',
  ReferenceTitle: '引用',
  KnowledgeCreteButtonTitle: '创建知识库',
  reportDrawerTitle: '分析报告',
  timeRangeSelectTip: '请选择异常发生时间',
  modelUsePrefixTip: '诊断报告由',
  modelUseSuffixTip: '生成',
  Diagnosis: '诊断',
  NoDiagnosisTask: '当前没有任务在运行，请上传异常文件进行诊断',
  SelectExceptionFile: '选择异常文件',
  StopDiagnosis: '停止诊断',
  RoleAssignment: '分配任务',
  ExpertDiagnosis: '专家分析',
  GroupDiscussion: '圆桌讨论',
  ReportGeneration: '报告生成',
  ReportDemonstration: '报告展示',
  router: {
    Chat: 'LLM对话',
    Knowledge: '知识库',
    KnowledgeChat: '知识问答',
    DatabaseChat: '数据库',
    Reports: '诊断历史',
    Dashboard: '首页',
    Diagnosis: '诊断'
  },
  'error-log': {
    log: '错误日志',
    pageUrl: '页面路径',
    startDate: '开始日期',
    endDate: '结束日期',
    github: 'Github 地址',
    search: '查询',
    reset: '重置',
    multiDel: '批量删除'
  }
}
