{"start_time": "1697303655", "end_time": "1697303804", "start_timestamp": "2023-10-15 01:14:15", "end_timestamp": "2023-10-15 01:16:44", "alerts": [{"receiver": "db-gpt", "status": "resolved", "alerts": [{"status": "resolved", "labels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "annotations": {"description": "node:ins:stdload1[ins=] = 1.75 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.75"}, "startsAt": "2023-10-15T01:14:49.467858611Z", "endsAt": "2023-10-15T01:15:49.467858611Z", "generatorURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9090/graph?g0.expr=node%3Ains%3Astdload1+%3E+1&g0.tab=1", "fingerprint": "ab4787213c7dd319"}], "groupLabels": {"alertname": "NodeLoadHigh"}, "commonLabels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "commonAnnotations": {"description": "node:ins:stdload1[ins=] = 1.75 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.75"}, "externalURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9093", "version": "4", "groupKey": "{}:{alertname=\"NodeLoadHigh\"}", "truncatedAlerts": 0}], "labels": ["FETCH_LARGE_DATA", "CORRELATED SUBQUERY"], "command": "python anomaly_trigger/main.py --anomaly FETCH_LARGE_DATA,CORRELATED_SUBQUERY", "script": "import  os\nimport re\nimport time\n\nimport psycopg2\n\n\nREPEATCOUNT = 1\nTIMELOGPATH = str(int(time.time())) + \"_tpch_trigger_time_log.txt\"\nTIMELOG = open(TIMELOGPATH, 'w+')\n\n\nclass Database():\n\n    def __init__(self):\n        self.conn = None\n        self.conn = psycopg2.connect(database='tpch',\n                                     user='xxxx',\n                                     password='xxxx',\n                                     host='xxxx',\n                                     port=5432)\n\n    def execute_sql(self, sql):\n        fail = 1\n        cur = self.conn.cursor()\n        i = 0\n        cnt = 3\n        while fail == 1 and i < cnt:\n            try:\n                fail = 0\n                cur.execute(sql)\n            except BaseException as error:\n                fail = 1\n                print(error)\n            res = []\n            if fail == 0:\n                res = cur.fetchall()\n            i = i + 1\n        if fail == 1:\n            # print(\"SQL Execution Fatal!!\", sql)\n            return 0, ''\n        elif fail == 0:\n            return 1, res\n\n\ndef all_sql_files():\n    res_path = \"{}/tpch-queries/\".format(\n        os.path.dirname(os.path.abspath(__file__)))\n    # all_file_list = list(filter(file_filter, os.listdir(res_path)))\n    # all_file_list = sorted(all_file_list, key=custom_sort)\n    all_file_list = [\n        '4.explain.sql']\n\n    print(all_file_list)\n    files_list = []\n    for file in all_file_list:\n        files_list.append(res_path + file)\n    return files_list\n\n\ndef custom_sort(item):\n    # 提取数字和字母部分\n    match = re.match(r'(\\d+)(\\D+)', item)\n    # 将数字部分转换为整数以进行比较\n    num_part = int(match.group(1))\n    # 返回元组以按数字和字母排序\n    return (num_part, match.group(2))\n\n\ndef file_filter(f):\n    if f[-4:] == '.sql' and 'schema' not in f and 'fkindexes' not in f:\n        return True\n    else:\n        return False\n\n\ndef get_sql_from_file(file_name):\n    file = open(file_name)\n    lines = file.readlines().copy()\n    sql = ''\n    for line in lines:\n        sql += line\n    sql = sql.replace('\n', ' ').replace('   ', ' ').replace('  ', ' ')\n    file.close()\n    return sql\n\n\ndef test_hint_from_file(sql_file):\n    db = Database()\n    sql = get_sql_from_file(sql_file)\n    success, result_cont = db.execute_sql(sql)\n    print(success, result_cont)\n\n\ndef test_all():\n    sql_files = all_sql_files()\n\n    for sql_file in list(sql_files):\n        if sql_file:\n            test_hint_from_file(sql_file)\n\n\ndef test_one():\n    res_path = \"{}/tpch-queries/\".format(\n        os.path.dirname(os.path.abspath(__file__)))\n    test_hint_from_file(res_path + '1.explain.sql')\n\n\nif __name__ == '__main__':\n    for i in range(0, REPEATCOUNT):\n        TIMELOG.write(str(int(time.time()))+\";\")\n        test_all()\n        TIMELOG.write(str(int(time.time()))+\"\n\")\n        TIMELOG.flush()\n\n    TIMELOG.close()\n", "description": "In an online shopping platform's database, when trying to retrieve a large amount of data and perform related subqueries to determine the inventory for each product, the execution of these subqueries may not be optimized, leading to slower performance in querying the inventory.\n", "workload": {"select o_orderpriority, count(*) as order_count from orders where o_orderdate >= date ':1' and o_orderdate < date ':1' + interval '3' month and exists ( select * from lineitem where l_orderkey = o_orderkey and l_commitdate < l_receiptdate ) group by o_orderpriority order by o_orderpriority;": 1}, "slow_queries": ["select o_orderpriority, count(*) as order_count from orders where o_orderdate >= date ':1' and o_orderdate < date ':1' + interval '3' month and exists ( select * from lineitem where l_orderkey = o_orderkey and l_commitdate < l_receiptdate ) group by o_orderpriority order by o_orderpriority;"], "exceptions": {"cpu": {"node_procs_running": [1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 2.0, 1.0, 3.0, 1.0, 1.0, 1.0, 2.0, 2.0, 1.0, 1.0, 1.0, 5.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 1.0, 2.0, 9.0, 9.0, 2.0, 1.0, 4.0, 1.0, 2.0, 1.0, 2.0, 3.0, 7.0, 8.0, 1.0, 5.0, 7.0], "node_procs_blocked": [0.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 4.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 2.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 2.0, 3.0, 3.0, 3.0, 2.0, 2.0, 3.0, 4.0, 3.0, 4.0, 3.0, 0.0, 2.0, 2.0, 2.0, 3.0, 2.0, 2.0], "node_entropy_available_bits": [3519.0, 3523.0, 3549.0, 3569.0, 3590.0, 3611.0, 3632.0, 3653.0, 3673.0, 3694.0, 3715.0, 3736.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0], "node_load1": [2.26, 2.26, 2.32, 2.37, 2.37, 2.42, 2.42, 2.47, 2.51, 2.51, 2.55, 2.55, 2.59, 2.62, 2.62, 2.65, 2.65, 2.68, 2.7, 2.7, 2.73, 2.73, 2.75, 2.77, 2.77, 2.79, 2.79, 2.81, 2.82, 2.82, 2.84, 2.84, 3.01, 3.01, 3.01, 3.01, 3.01, 3.01, 3.01, 3.01, 3.01, 3.01, 3.09, 3.08, 3.08, 3.23, 3.23, 3.21, 3.28, 3.28]}, "io": {"node_filesystem_size_bytes": [212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0], "1-(node_filesystem_free_bytes": [0.432399308269829, 0.4324000221256725, 0.4324000414190736, 0.43240006071247483, 0.43240006071247483, 0.43240006071247483, 0.43240006071247483, 0.43240006071247483, 0.43240008000587604, 0.43240009929927714, 0.43240009929927714, 0.43240009929927714, 0.43240009929927714, 0.43240009929927714, 0.43240009929927714, 0.43240009929927714, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.4324008710353242, 0.432401179729743, 0.43262201199960804, 0.43304476900618516, 0.43328848324984537, 0.43352725838280437, 0.4338757743816547, 0.43422583385259916, 0.4344720176516099, 0.4347220601308559, 0.43500389813524065, 0.4351809343844353, 0.4354226421143731, 0.43570880184062133, 0.4360384874799236, 0.4363498058013058, 0.4368026605137181, 0.4368026605137181, 0.4368026605137181, 0.4368026605137181], "irate(node_disk_writes_completed_total": [1.6666666666666667, 1.0, 10.666666666666666, 12.666666666666666, 1.0, 1.0, 1.6666666666666667, 0.0, 1.6666666666666667, 0.0, 1.6666666666666667, 0.6666666666666666, 2.3333333333333335, 2.6666666666666665, 1.6666666666666667, 12.0, 79.0, 106.0, 100.33333333333333, 94.0, 130.33333333333334, 137.33333333333334, 91.66666666666667, 120.0, 72.0, 69.33333333333333, 73.66666666666667, 82.0, 95.66666666666667, 69.0, 72.0, 82.66666666666667, 122.66666666666667, 99.33333333333333, 93.66666666666667, 101.33333333333333, 96.66666666666667, 178.0, 106.33333333333333, 365.3333333333333, 411.6666666666667, 2007.6666666666667, 562.6666666666666, 71.0, 72.33333333333333, 74.33333333333333, 78.33333333333333, 73.0, 622.3333333333334, 2095.0], "node_disk_io_now": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "memory": {"node_memory_Inactive_anon_bytes": [235458560.0, 238903296.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 238989312.0, 241086464.0, 241086464.0, 241086464.0, 241086464.0, 241086464.0, 241086464.0, 289685504.0, 978120704.0, 1583427584.0, 1958223872.0], "node_memory_Buffers_bytes": [19365888.0, 19382272.0, 19382272.0, 17657856.0, 14364672.0, 11993088.0, 10735616.0, 9658368.0, 8556544.0, 7970816.0, 7974912.0, 7811072.0, 7643136.0, 7655424.0, 7569408.0, 7507968.0, 7507968.0, 7458816.0, 7409664.0, 7348224.0, 7299072.0, 7192576.0, 7069696.0, 6963200.0, 6877184.0, 6828032.0, 6717440.0, 6787072.0, 6746112.0, 6684672.0, 6672384.0, 7106560.0, 7098368.0, 7098368.0, 7098368.0, 7098368.0, 7098368.0, 7098368.0, 7094272.0, 7102464.0, 7106560.0, 7114752.0, 7102464.0, 7106560.0, 7110656.0, 7102464.0, 7557120.0, 7532544.0, 7397376.0, 7286784.0]}, "network": {"node_sockstat_TCP_tw": [9.0, 9.0, 9.0, 8.0, 10.0, 9.0, 9.0, 9.0, 8.0, 9.0, 9.0, 9.0, 9.0, 8.0, 9.0, 9.0, 9.0, 9.0, 6.0, 7.0, 8.0, 8.0, 8.0, 6.0, 6.0, 6.0, 7.0, 7.0, 6.0, 7.0, 7.0, 7.0, 7.0, 6.0, 7.0, 7.0, 7.0, 7.0, 5.0, 6.0, 7.0, 6.0, 6.0, 7.0, 8.0, 8.0, 8.0, 8.0, 7.0, 7.0], "node_sockstat_TCP_orphan": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_netstat_Tcp_PassiveOpens": [0.3333333333333333, 0.3333333333333333, 0.0, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "node_sockstat_TCP_alloc": [22.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0], "node_sockstat_TCP_inuse": [13.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0, 14.0]}}}