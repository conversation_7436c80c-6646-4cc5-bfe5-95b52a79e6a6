// sidebar
$menuText:#999999;
$menuActiveText:#41b584;
$subMenuActiveText:#41b584; //https://github.com/ElemeFE/element/issues/12951

$menuBg:#ffffff;
$menuHover:#ecf8f3;

$subMenuBg:#ffffff;
$subMenuHover:#ecf8f3;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
}
