{"start_time": "1697303864", "end_time": "1697303935", "start_timestamp": "2023-10-15 01:17:44", "end_timestamp": "2023-10-15 01:18:55", "alerts": [{"receiver": "db-gpt", "status": "resolved", "alerts": [{"status": "resolved", "labels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "annotations": {"description": "node:ins:stdload1[ins=] = 1.59 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.59"}, "startsAt": "2023-10-15T01:17:49.467858611Z", "endsAt": "2023-10-15T01:23:49.467858611Z", "generatorURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9090/graph?g0.expr=node%3Ains%3Astdload1+%3E+1&g0.tab=1", "fingerprint": "ab4787213c7dd319"}], "groupLabels": {"alertname": "NodeLoadHigh"}, "commonLabels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "commonAnnotations": {"description": "node:ins:stdload1[ins=] = 1.59 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.59"}, "externalURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9093", "version": "4", "groupKey": "{}:{alertname=\"NodeLoadHigh\"}", "truncatedAlerts": 0}], "labels": ["highly concurrent commits or highly concurrent inserts"], "command": "python anomaly_trigger/main.py --anomaly INSERT_LARGE_DATA", "script": "import  psycopg2\nimport sys\nsys.path.append('/root/DB-GPT/')\nimport time\nimport datetime\nimport random\nimport yaml\nfrom multiprocessing.pool import *\n\nclass DBArgs(object):\n\n    def __init__(self, dbtype, config, dbname=None):\n        self.dbtype = dbtype\n        if self.dbtype == 'mysql':\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'com.mysql.jdbc.Driver'\n            self.jdbc = 'jdbc:mysql://'\n        else:\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'org.postgresql.Driver'\n            self.jdbc = 'jdbc:postgresql://'\n\nclass Database():\n    def __init__(self, args, timeout=-1):\n        self.args = args\n        self.conn = self.resetConn(timeout)\n\n\n        # self.schema = self.compute_table_schema()\n\n    def resetConn(self, timeout=-1):\n        conn = psycopg2.connect(database=self.args.dbname,\n                                            user=self.args.user,\n                                            password=self.args.password,\n                                            host=self.args.host,\n                                            port=self.args.port)\n        return conn\n\n    def execute_sqls(self,sql):\n        self.conn =self.resetConn(timeout=-1)\n        cur = self.conn.cursor()\n        cur.execute(sql)\n        self.conn.commit()\n        cur.close()\n        self.conn.close()\n\n    def execute_sql_duration(self, duration, sql, max_id=0, commit_interval=500):\n        self.conn = self.resetConn(timeout=-1)\n        cursor = self.conn.cursor()\n        start = time.time()\n        cnt = 0\n        if duration > 0:\n            while (time.time() - start) < duration:\n                if max_id > 0:\n                    id = random.randint(1, max_id - 1)\n                    cursor.execute(sql + str(id) + ';')\n                else:\n                    cursor.execute(sql)\n                cnt += 1\n                if cnt % commit_interval == 0:\n                    self.conn.commit()\n        else:\n            print(\"error, the duration should be larger than 0\")\n        self.conn.commit()\n        cursor.close()\n        self.conn.close()\n        return cnt\n\n    def concurrent_execute_sql(self, threads, duration, sql, max_id=0, commit_interval=500):\n        pool = ThreadPool(threads)\n        results = [pool.apply_async(self.execute_sql_duration, (duration, sql, max_id, commit_interval)) for _ in range(threads)]\n        pool.close()\n        pool.join()\n        return results\n    \ndef init():\n    #add the config\n    config_path = \"/root/DB-GPT/config/tool_config.yaml\"\n    with open(config_path, 'r') as config_file:\n        config = yaml.safe_load(config_file) \n    db_args =DBArgs('pgsql', config)\n    return db_args\n\n#create a table\ndef create_table(table_name,colsize, ncolumns):\n    db=Database(init())\n    column_definitions = ', '.join(f'name{i} varchar({colsize})' for i in range(ncolumns))\n    creat_sql = f'CREATE TABLE {table_name} (id int, {column_definitions}, time timestamp);'\n    db.execute_sqls(creat_sql)\n\n#delete the table\ndef delete_table(table_name):\n    db=Database(init())\n    delete_sql=f'DROP TABLE if exists {table_name}'\n    db.execute_sqls(delete_sql)\n\n#print the current time\ndef print_time():\n    current_time = datetime.datetime.now()\n    formatted_time = current_time.strftime(\"%Y-%m-%d %H:%M:%S\")\n    print(formatted_time)\n\n\ndef insert_large_data(threads,duration,ncolumns,nrows,colsize,table_name='table1'):\n\n    print_time()\n    #Delete undeleted tables\n    delete_table(table_name)\n    #create a new table\n    create_table(table_name,colsize, ncolumns)\n    db=Database(init())\n    #insert the data\n    #insert_definitions = ', '.join(f'repeat(round(random()*999)::text,{(colsize//3)})' for i in range(ncolumns))\n    insert_definitions = ', '.join(f'(SELECT substr(md5(random()::text), 1, {colsize}))' for i in range(ncolumns))\n    insert_data=f'insert into {table_name} select generate_series(1,{nrows}),{insert_definitions}, now();'\n    db.concurrent_execute_sql(threads,duration,insert_data,commit_interval=1)\n\n    #delete the table\n    delete_table(table_name)\n    \n    #print the end time\n    print_time()\nif __name__ == \"__main__\":\n    # Number of threads to use for concurrent inserts\n    num_threads = 98\n    \n    # Duration for which to run the inserts (in seconds)\n    insert_duration = None\n    \n    # Number of columns in the table\n    num_columns = 16\n    \n    # Number of rows to insert\n    num_rows = 66\n    \n    # Size of each column (in characters)\n    column_size = 48\n    \n    # Table name\n    table_name = 'table1'\n    \n    # Call the insert_large_data function\n    insert_large_data(num_threads, insert_duration, num_columns, num_rows, column_size, table_name)\n", "description": "In a real-life scenario, 98 sensors are generating a large amount of data that needs to be inserted into a database simultaneously. The database table has 16 columns, each with a size of 48 characters, and there are 66 rows of data to be inserted. By simulating this process, we can observe and analyze any exceptions or issues that might arise due to this high-volume data insertion.\n", "workload": {"insert into 'table1' select generate_series(1,66),(SELECT substr(md5(random()::text), 1, 48)), now();": 98}, "slow_queries": [], "exceptions": {"cpu": {"node_procs_blocked": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0], "node_entropy_available_bits": [3504.0, 3507.0, 3507.0, 3535.0, 3606.0, 3676.0, 3750.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0]}, "io": {"node_filesystem_size_bytes": [212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0], "irate(node_disk_reads_completed_total": [0.0, 123.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 19.666666666666668, 0.0, 0.0, 0.0, 22.0, 0.0, 0.3333333333333333, 0.0, 19.0, 0.0, 0.0, 0.0, 30.333333333333332, 0.0], "node_disk_io_now": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_disk_read_bytes_total": [0.0, 3107498.6666666665, 0.0, 0.0, 0.0, 0.0, 0.0, 1365.3333333333333, 0.0, 0.0, 1112746.6666666667, 0.0, 0.0, 0.0, 94208.0, 0.0, 1365.3333333333333, 0.0, 1058133.3333333333, 0.0, 0.0, 0.0, 1181013.3333333333, 0.0], "irate(node_disk_read_time_seconds_total": [0.0, 0.06633333334078391, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.17766666668467224, 0.0, 0.0, 0.0, 0.004666666655490796, 0.0, 0.0, 0.0, 0.04833333333954215, 0.0, 0.0, 0.0, 0.06333333331470688, 0.0]}, "memory": {}, "network": {"node_sockstat_TCP_tw": [11.0, 11.0, 11.0, 10.0, 10.0, 10.0, 10.0, 10.0, 8.0, 8.0, 8.0, 4.0, 4.0, 7.0, 7.0, 7.0, 6.0, 7.0, 6.0, 6.0, 6.0, 6.0, 6.0, 6.0], "node_sockstat_TCP_orphan": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_netstat_Tcp_PassiveOpens": [0.0, 1.3333333333333333, 0.0, 32.666666666666664, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.6666666666666666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "node_sockstat_TCP_alloc": [22.0, 22.0, 22.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 120.0, 22.0], "node_sockstat_TCP_inuse": [13.0, 13.0, 13.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 111.0, 13.0]}}}