{"start_time": "1697297388", "end_time": "1697297503", "start_timestamp": "2023-10-14 23:29:48", "end_timestamp": "2023-10-14 23:31:43", "alerts": [{"receiver": "db-gpt", "status": "resolved", "alerts": [{"status": "resolved", "labels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "annotations": {"description": "node:ins:stdload1[ins=] = 1.50 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.50"}, "startsAt": "2023-10-14T23:29:49.467858611Z", "endsAt": "2023-10-15T00:26:49.467858611Z", "generatorURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9090/graph?g0.expr=node%3Ains%3Astdload1+%3E+1&g0.tab=1", "fingerprint": "ab4787213c7dd319"}], "groupLabels": {"alertname": "NodeLoadHigh"}, "commonLabels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "commonAnnotations": {"description": "node:ins:stdload1[ins=] = 1.50 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.50"}, "externalURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9093", "version": "4", "groupKey": "{}:{alertname=\"NodeLoadHigh\"}", "truncatedAlerts": 0}], "labels": ["too many indexes"], "command": "python anomaly_trigger/main.py --anomaly REDUNDANT_INDEX", "script": "import  psycopg2\nimport sys\nsys.path.append('/root/DB-GPT/')\nimport time\nimport datetime\nimport random\nimport yaml\nfrom multiprocessing.pool import *\n\n\nclass DBArgs(object):\n\n    def __init__(self, dbtype, config, dbname=None):\n        self.dbtype = dbtype\n        if self.dbtype == 'mysql':\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'com.mysql.jdbc.Driver'\n            self.jdbc = 'jdbc:mysql://'\n        else:\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'org.postgresql.Driver'\n            self.jdbc = 'jdbc:postgresql://'\n\nclass Database():\n    def __init__(self, args, timeout=-1):\n        self.args = args\n        self.conn = self.resetConn(timeout)\n\n\n        # self.schema = self.compute_table_schema()\n\n    def resetConn(self, timeout=-1):\n        conn = psycopg2.connect(database=self.args.dbname,\n                                            user=self.args.user,\n                                            password=self.args.password,\n                                            host=self.args.host,\n                                            port=self.args.port)\n        return conn\n    \n    def execute_sqls(self,sql):\n        self.conn =self.resetConn(timeout=-1)\n        cur = self.conn.cursor()\n        cur.execute(sql)\n        self.conn.commit()\n        cur.close()\n        self.conn.close()\n\n    def execute_sql_duration(self, duration, sql, max_id=0, commit_interval=500):\n        self.conn = self.resetConn(timeout=-1)\n        cursor = self.conn.cursor()\n        start = time.time()\n        cnt = 0\n        if duration > 0:\n            while (time.time() - start) < duration:\n                if max_id > 0:\n                    id = random.randint(1, max_id - 1)\n                    cursor.execute(sql + str(id) + ';')\n                else:\n                    cursor.execute(sql)\n                cnt += 1\n                if cnt % commit_interval == 0:\n                    self.conn.commit()\n        else:\n            print(\"error, the duration should be larger than 0\")\n        self.conn.commit()\n        cursor.close()\n        self.conn.close()\n        return cnt\n\n    def concurrent_execute_sql(self, threads, duration, sql, max_id=0, commit_interval=500):\n        pool = ThreadPool(threads)\n        results = [pool.apply_async(self.execute_sql_duration, (duration, sql, max_id, commit_interval)) for _ in range(threads)]\n        pool.close()\n        pool.join()\n        return results\n    \n    def build_index(self, table_name, idx_num):\n        self.conn = self.resetConn(timeout=-1)\n        cursor = self.conn.cursor()\n        \n        for i in range(0, idx_num):\n            the_sql = 'CREATE INDEX index_' + table_name + '_' + str(i) + ' ON ' + table_name + '(name' + str(i) + ');'\n            print(the_sql)\n            cursor.execute(the_sql)\n\n        \n        self.conn.commit()\n        self.conn.close()\n        return\n\n\n    \n    def drop_index(self,table_name):\n        self.conn = self.resetConn(timeout=-1)\n        cursor = self.conn.cursor()\n        cursor.execute(\"select indexname from pg_indexes where tablename='\"+table_name+\"';\")\n        idxs = cursor.fetchall()\n        for idx in idxs:\n            the_sql = 'DROP INDEX ' + idx[0] + ';'\n            cursor.execute(the_sql)\n            print(the_sql)\n        self.conn.commit()\n        self.conn.close()\n        return\n\n\ndef init():\n    #add the config\n    config_path = \"/root/DB-GPT/config/tool_config.yaml\"\n    with open(config_path, 'r') as config_file:\n        config = yaml.safe_load(config_file) \n    db_args =DBArgs('pgsql', config)\n    return db_args\n\n\n#create a table\ndef create_table(table_name,colsize, ncolumns):\n    db=Database(init())\n    column_definitions = ', '.join(f'name{i} varchar({colsize})' for i in range(ncolumns))\n    creat_sql = f'CREATE TABLE {table_name} (id int, {column_definitions}, time timestamp);'\n    db.execute_sqls(creat_sql)\n\n#delete the table\ndef delete_table(table_name):\n    db=Database(init())\n    delete_sql=f'DROP TABLE if exists {table_name}'\n    db.execute_sqls(delete_sql)\n\n#print the current time\ndef print_time():\n    current_time = datetime.datetime.now()\n    formatted_time = current_time.strftime(\"%Y-%m-%d %H:%M:%S\")\n    print(formatted_time)\n\ndef redundent_index(threads,duration,ncolumns,nrows,colsize,nindex,table_name='table1'):\n    #create a new table\n    print_time()\n    delete_table(table_name)\n    create_table(table_name,colsize, ncolumns)\n    db=Database(init())\n    # insert some data to be updated \n    insert_definitions = ', '.join(f'(SELECT substr(md5(random()::text), 1, {colsize}))' for i in range(ncolumns))\n    insert_data=f'insert into {table_name} select generate_series(1,{nrows}),{insert_definitions}, now();' \n    db.execute_sqls(insert_data) \n\n    #initialization of the indexes\n    nindex=int((nindex*ncolumns)/10)\n    db.build_index(table_name,nindex)\n    id_index='CREATE INDEX index_'+table_name+'_id ON '+table_name+'(id);'\n    db.execute_sqls(id_index)\n\n    #lock_contention\n    pool = Pool(threads)\n    for _ in range(threads):\n        pool.apply_async(\n            lock, (table_name, ncolumns, colsize, duration, nrows))\n    pool.close()\n    pool.join()\n\n    #drop the index\n    db.drop_index(table_name)\n\n    #delete the table\n    delete_table(table_name)\n    print_time()\n\n\ndef lock(table_name, ncolumns, colsize, duration, nrows):\n    args=init()\n    start = time.time()\n    #lock_contention\n    while time.time()-start < duration:\n        conn = psycopg2.connect(database=args.dbname, user=args.user, password=args.password,\n                                        host=args.host, port=args.port)\n        cur = conn.cursor()\n        while time.time()-start < duration:\n            col_name = random.randint(0, ncolumns-1)\n            row_name = random.randint(1, nrows-1)\n            lock_contention = f'update {table_name} set name{col_name}=(SELECT substr(md5(random()::text), 1, {colsize})) where id ={row_name}'\n            #db.concurrent_execute_sql(threads,duration,lock_contention,nrows)\n            cur.execute(lock_contention)\n            conn.commit()\n        conn.commit()\n        conn.close()\n\nif __name__ == \"__main__\":\n    # Number of threads to use for concurrent inserts\n    num_threads = 9\n    \n    # Duration for which to run the inserts (in seconds)\n    insert_duration = None\n    \n    # Number of columns in the table\n    num_columns = 57\n    \n    # Number of rows to insert\n    num_rows = 799006\n    \n    # Size of each column (in characters)\n    column_size = 56\n    \n    # Table name\n    table_name = 'table1'\n    \n    nindex=6\n    \n    # Call the insert_large_data function\n    redundent_index(num_threads, insert_duration, num_columns, num_rows, column_size, nindex,table_name)\n", "description": "In a digital marketing company, the database contains 57 columns and 799,006 rows of customer information. Each column has a size of 56 characters. Initially, a large number of indexes are created for customer attributes such as name, age, and gender. Then, nine users simultaneously perform queries on the customer data, and the indexes are deleted after the queries are completed. This simulates the additional storage and performance impact caused by redundant indexes.\n", "workload": {"CREATE INDEX index_table1_0 ON table1(name0);": 1, "CREATE INDEX index_table1_1 ON table1(name1);": 1, "CREATE INDEX index_table1_2 ON table1(name2);": 1, "CREATE INDEX index_table1_3 ON table1(name3);": 1, "CREATE INDEX index_table1_4 ON table1(name4);": 1, "CREATE INDEX index_table1_5 ON table1(name5);": 1, "CREATE INDEX index_table1_id ON table1(id);": 1, "INSERT INTO table1 SELECT generate_series(1,799006),(SELECT substr(md5(random()::text), 1, 56), ... , now();": 1, "UPDATE table1 SET nameN=(SELECT substr(md5(random()::text), 1, 56) WHERE id =M;": 9}, "slow_queries": [], "exceptions": {"cpu": {"node_procs_running": [1.0, 1.0, 2.0, 7.0, 1.0, 3.0, 4.0, 5.0, 5.0, 7.0, 9.0, 6.0, 3.0, 4.0, 9.0, 3.0, 6.0, 5.0, 1.0, 16.0, 8.0, 7.0, 8.0, 12.0, 2.0, 3.0, 8.0, 2.0, 1.0, 17.0, 1.0, 7.0, 3.0, 1.0, 6.0, 6.0, 5.0, 8.0, 15.0], "node_procs_blocked": [0.0, 0.0, 1.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 7.0, 2.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 4.0, 2.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0], "node_entropy_available_bits": [3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3500.0, 3505.0, 3512.0, 3517.0, 3523.0, 3528.0, 3532.0, 3537.0, 3612.0, 3663.0, 3727.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0]}, "io": {"node_filesystem_size_bytes": [212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0], "irate(node_disk_reads_completed_total": [12.666666666666666, 0.0, 8.0, 2.0, 13.333333333333334, 5.333333333333333, 304.0, 0.6666666666666666, 0.3333333333333333, 0.3333333333333333, 0.3333333333333333, 0.3333333333333333, 0.3333333333333333, 0.0, 0.6666666666666666, 0.0, 0.6666666666666666, 3.0, 0.0, 599.3333333333334, 10178.666666666666, 2960.3333333333335, 978.0, 538.0, 364.3333333333333, 90.33333333333333, 92.33333333333333, 955.3333333333334, 1650.3333333333333, 2002.0, 136.33333333333334, 79.66666666666667, 231.33333333333334, 18.666666666666668, 153.66666666666666, 124.0, 49.0, 13.666666666666666, 45.0], "node_disk_io_now": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_disk_read_bytes_total": [132437.33333333334, 0.0, 32768.0, 8192.0, 72362.66666666667, 62805.333333333336, 4501504.0, 2730.6666666666665, 1365.3333333333333, 1365.3333333333333, 1365.3333333333333, 1365.3333333333333, 1365.3333333333333, 0.0, 2730.6666666666665, 0.0, 4096.0, 12288.0, 0.0, 8385877.333333333, 133707093.33333333, 25123498.666666668, 8075946.666666667, 5025792.0, 3840682.6666666665, 756394.6666666666, 686762.6666666666, 8050005.333333333, 14834346.666666666, 16657066.666666666, 1148245.3333333333, 670378.6666666666, 5040810.666666667, 109226.66666666667, 802816.0, 1241088.0, 1033557.3333333334, 107861.33333333333, 705877.3333333334], "irate(node_disk_read_time_seconds_total": [0.045999999972991645, 0.0, 0.0016666666682188709, 0.0066666666728754835, 0.033000000015211604, 0.023000000005898375, 0.4609999999714394, 0.004666666694295903, 0.0, 0.004666666655490796, 0.0, 0.004666666655490796, 0.0, 0.0, 0.003999999995964269, 0.0, 0.004666666694295903, 0.0, 0.0, 0.30633333333147067, 15.914333333338922, 3.248666666642142, 2.3263333333500973, 1.8656666666502133, 0.5650000000217309, 0.6509999999931703, 0.2989999999990687, 6.897666666656733, 9.868333333327124, 1.5496666666585952, 0.34300000003228587, 0.20833333333333334, 0.4693333333125338, 0.07466666668187827, 0.7996666666585952, 0.13333333334109435, 0.4600000000015522, 0.01866666666076829, 0.04833333333954215]}, "memory": {"node_memory_Inactive_anon_bytes": [211820544.0, 211820544.0, 211824640.0, 211824640.0, 209211392.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209301504.0, 209088512.0, 209088512.0, 209104896.0, 209104896.0, 209104896.0, 209104896.0, 209104896.0, 209121280.0, 209108992.0, 209342464.0, 209342464.0, 209342464.0, 209526784.0, 209526784.0, 209526784.0, 209592320.0, 209592320.0, 209592320.0, 209596416.0, 209596416.0, 209596416.0]}, "network": {"node_sockstat_TCP_tw": [9.0, 10.0, 10.0, 10.0, 8.0, 9.0, 10.0, 10.0, 10.0, 10.0, 10.0, 4.0, 10.0, 10.0, 10.0, 10.0, 11.0, 11.0, 11.0, 10.0, 10.0, 11.0, 11.0, 11.0, 9.0, 9.0, 10.0, 10.0, 10.0, 10.0, 10.0, 11.0, 9.0, 9.0, 8.0, 9.0, 10.0, 10.0, 10.0], "node_sockstat_TCP_orphan": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_netstat_Tcp_PassiveOpens": [0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.6666666666666666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 3.3333333333333335, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6666666666666666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "node_sockstat_TCP_alloc": [21.0, 21.0, 22.0, 22.0, 21.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 21.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0, 31.0], "node_sockstat_TCP_inuse": [12.0, 12.0, 13.0, 13.0, 12.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 12.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0]}}}