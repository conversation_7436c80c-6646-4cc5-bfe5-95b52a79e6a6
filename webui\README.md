
# vue3-admin-template

A new generation vue3 admin template, It's easy and fast!

suggestion the Node.js >= v16.20

[Recommended node](https://nodejs.org/download/release/v16.20.2/)

## Documents

- [Official Documentation](https://github.jzfai.top/vue3-admin-doc/)

- [中文官网](https://github.jzfai.top/vue3-admin-cn-doc/)


## Online experience

[Access address](https://github.jzfai.top/vue3-admin-template)

[国内体验地址](https://github.jzfai.top/vue3-admin-template)



## Related items

The framework is available in js,ts, plus and electron versions
- js version：[vue3-admin-template](https://github.com/jzfai/vue3-admin-template.git) -- basic version
- ts version：[vue3-element-ts](https://github.com/jzfai/vue3-admin-ts.git)
- ts version for plus：[vue3-element-plus](https://github.com/jzfai/vue3-admin-plus.git)
- ts version for electron：[vue3-element-electron](https://github.com/jzfai/vue3-admin-electron.git)
- java Micro-service background data：[micro-service-single](https://github.com/jzfai/micro-service-single)


## Build Setup

```bash
# clone the project
git clone https://github.com/jzfai/vue3-admin-template.git

# enter the project directory
cd vue3-admin-template

# pnpm address https://pnpm.io/zh/motivation
# install dependency(Recommend use pnpm)
# you can  use "npm -g i pnpm" to install pnpm 
pnpm i

# develop
pnpm run dev
```

## Build

```bash
# build for test environment
pnpm run build-test

# build for production environment
pnpm run  build
```

## Others

```bash
# preview the release environment effect
pnpm run preview

# code format check
pnpm run lint

```


## Browsers support

Note: Vue3 is not supported the Internet Explorer


## Discussion and Communication
[WeChat group](https://github.jzfai.top/file/images/wx-groud.png)



