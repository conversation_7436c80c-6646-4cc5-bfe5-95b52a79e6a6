// Generated by 'unplugin-auto-import'
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const axiosReq: typeof import('../src/utils/axios-req.js')['default']
  const bus: typeof import('../src/utils/bus.js')['default']
  const casHandleChange: typeof import('../src/hooks/use-element.js')['casHandleChange']
  const cloneDeep: typeof import('../src/hooks/use-common.js')['cloneDeep']
  const closeElLoading: typeof import('../src/hooks/use-element.js')['closeElLoading']
  const commonUtil: typeof import('../src/utils/common-util.js')['default']
  const computed: typeof import('vue')['computed']
  const copyValueToClipboard: typeof import('../src/hooks/use-common.js')['copyValueToClipboard']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const effectScope: typeof import('vue')['effectScope']
  const elConfirm: typeof import('../src/hooks/use-element.js')['elConfirm']
  const elConfirmNoCancelBtn: typeof import('../src/hooks/use-element.js')['elConfirmNoCancelBtn']
  const elLoading: typeof import('../src/hooks/use-element.js')['elLoading']
  const elMessage: typeof import('../src/hooks/use-element.js')['elMessage']
  const elNotify: typeof import('../src/hooks/use-element.js')['elNotify']
  const fetchData: typeof import('../src/utils/fetchData.js')['fetchData']
  const filterAsyncRouter: typeof import('../src/hooks/use-permission.js')['filterAsyncRouter']
  const filterAsyncRouterByCodes: typeof import('../src/hooks/use-permission.js')['filterAsyncRouterByCodes']
  const filterAsyncRoutesByMenuList: typeof import('../src/hooks/use-permission.js')['filterAsyncRoutesByMenuList']
  const filterAsyncRoutesByRoles: typeof import('../src/hooks/use-permission.js')['filterAsyncRoutesByRoles']
  const freshRouter: typeof import('../src/hooks/use-permission.js')['freshRouter']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getLangInstance: typeof import('../src/hooks/use-common.js')['getLangInstance']
  const getQueryParam: typeof import('../src/hooks/use-self-router.js')['getQueryParam']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isExternal: typeof import('../src/hooks/use-layout.js')['isExternal']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const langTitle: typeof import('../src/hooks/use-common.js')['langTitle']
  const markRaw: typeof import('vue')['markRaw']
  const markdownConfig: typeof import('../src/utils/markdownConfig.js')['default']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const progressClose: typeof import('../src/hooks/use-permission.js')['progressClose']
  const progressStart: typeof import('../src/hooks/use-permission.js')['progressStart']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resetRouter: typeof import('../src/hooks/use-permission.js')['resetRouter']
  const resetState: typeof import('../src/hooks/use-permission.js')['resetState']
  const resizeHandler: typeof import('../src/hooks/use-layout.js')['resizeHandler']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const resolveDirective: typeof import('vue')['resolveDirective']
  const routeInfo: typeof import('../src/hooks/use-self-router.js')['routeInfo']
  const routerBack: typeof import('../src/hooks/use-self-router.js')['routerBack']
  const routerPush: typeof import('../src/hooks/use-self-router.js')['routerPush']
  const routerReplace: typeof import('../src/hooks/use-self-router.js')['routerReplace']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sleepTimeout: typeof import('../src/hooks/use-common.js')['sleepTimeout']
  const storeToRefs: typeof import('pinia/dist/pinia')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBasicStore: typeof import('../src/store/basic.js')['useBasicStore']
  const useConfigStore: typeof import('../src/store/config.js')['useConfigStore']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useElement: typeof import('../src/hooks/use-element.js')['useElement']
  const useErrorLog: typeof import('../src/hooks/use-error-log.js')['useErrorLog']
  const useLink: typeof import('vue-router')['useLink']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTable: typeof import('../src/hooks/use-table.js')['useTable']
  const useTagsViewStore: typeof import('../src/store/tags-view.js')['useTagsViewStore']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
