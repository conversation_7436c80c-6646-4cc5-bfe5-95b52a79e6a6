{"start_time": "1697297150", "end_time": "1697297328", "start_timestamp": "2023-10-14 23:25:50", "end_timestamp": "2023-10-14 23:28:48", "alerts": [{"receiver": "db-gpt", "status": "resolved", "alerts": [{"status": "resolved", "labels": {"alertname": "NodeCpuHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "annotations": {"description": "node:ins:cpu_usage[ins=] = 0.99 > 70%\n", "summary": "WARN NodeCpuHigh @************:9100 0.99"}, "startsAt": "2023-10-14T23:26:49.467858611Z", "endsAt": "2023-10-14T23:27:49.467858611Z", "generatorURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9090/graph?g0.expr=node%3Ains%3Acpu_usage_1m+%3E+0.7&g0.tab=1", "fingerprint": "b97dc2cd4ed607f5"}], "groupLabels": {"alertname": "NodeCpuHigh"}, "commonLabels": {"alertname": "NodeCpuHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "commonAnnotations": {"description": "node:ins:cpu_usage[ins=] = 0.99 > 70%\n", "summary": "WARN NodeCpuHigh @************:9100 0.99"}, "externalURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9093", "version": "4", "groupKey": "{}:{alertname=\"NodeCpuHigh\"}", "truncatedAlerts": 0}], "labels": ["highly deletes"], "command": "python anomaly_trigger/main.py --anomaly VACUUM", "script": "import  psycopg2\nimport sys\nsys.path.append('/root/DB-GPT/')\nimport time\nimport datetime\nimport random\nimport yaml\nfrom multiprocessing.pool import *\n\n\nclass DBArgs(object):\n\n    def __init__(self, dbtype, config, dbname=None):\n        self.dbtype = dbtype\n        if self.dbtype == 'mysql':\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'com.mysql.jdbc.Driver'\n            self.jdbc = 'jdbc:mysql://'\n        else:\n            self.host = config['host']\n            self.port = config['port']\n            self.user = config['user']\n            self.password = config['password']\n            self.dbname = dbname if dbname else config['dbname']\n            self.driver = 'org.postgresql.Driver'\n            self.jdbc = 'jdbc:postgresql://'\n\nclass Database():\n    def __init__(self, args, timeout=-1):\n        self.args = args\n        self.conn = self.resetConn(timeout)\n\n\n        # self.schema = self.compute_table_schema()\n\n    def resetConn(self, timeout=-1):\n        conn = psycopg2.connect(database=self.args.dbname,\n                                            user=self.args.user,\n                                            password=self.args.password,\n                                            host=self.args.host,\n                                            port=self.args.port)\n        return conn\n    \n    def execute_sqls(self,sql):\n        self.conn =self.resetConn(timeout=-1)\n        cur = self.conn.cursor()\n        cur.execute(sql)\n        self.conn.commit()\n        cur.close()\n        self.conn.close()\n\n    def execute_sql_duration(self, duration, sql, max_id=0, commit_interval=500):\n        self.conn = self.resetConn(timeout=-1)\n        cursor = self.conn.cursor()\n        start = time.time()\n        cnt = 0\n        if duration > 0:\n            while (time.time() - start) < duration:\n                if max_id > 0:\n                    id = random.randint(1, max_id - 1)\n                    cursor.execute(sql + str(id) + ';')\n                else:\n                    cursor.execute(sql)\n                cnt += 1\n                if cnt % commit_interval == 0:\n                    self.conn.commit()\n        else:\n            print(\"error, the duration should be larger than 0\")\n        self.conn.commit()\n        cursor.close()\n        self.conn.close()\n        return cnt\n\n    def concurrent_execute_sql(self, threads, duration, sql, max_id=0, commit_interval=500):\n        pool = ThreadPool(threads)\n        results = [pool.apply_async(self.execute_sql_duration, (duration, sql, max_id, commit_interval)) for _ in range(threads)]\n        pool.close()\n        pool.join()\n        return results\n    \ndef init():\n    #add the config\n    config_path = \"/root/DB-GPT/config/tool_config.yaml\"\n    with open(config_path, 'r') as config_file:\n        config = yaml.safe_load(config_file) \n    db_args =DBArgs('pgsql', config)\n    return db_args\n\n\n#create a table\ndef create_table(table_name,colsize, ncolumns):\n    db=Database(init())\n    column_definitions = ', '.join(f'name{i} varchar({colsize})' for i in range(ncolumns))\n    creat_sql = f'CREATE TABLE {table_name} (id int, {column_definitions}, time timestamp);'\n    db.execute_sqls(creat_sql)\n\n#delete the table\ndef delete_table(table_name):\n    db=Database(init())\n    delete_sql=f'DROP TABLE if exists {table_name}'\n    db.execute_sqls(delete_sql)\n\n#print the current time\ndef print_time():\n    current_time = datetime.datetime.now()\n    formatted_time = current_time.strftime(\"%Y-%m-%d %H:%M:%S\")\n    print(formatted_time)\n\n\ndef vacuum(threads,duration,ncolumns,nrows,colsize,table_name='table1'):\n    db=Database(init())\n    #create a new table\n    print_time()\n    delete_table(table_name)\n    create_table(table_name,colsize, ncolumns)\n\n    # insert some data to be deleted\n    insert_definitions = ', '.join(f'(SELECT substr(md5(random()::text), 1, {colsize}))' for i in range(ncolumns))\n    insert_data=f'insert into {table_name} select generate_series(1,{nrows}),{insert_definitions}, now();' \n    db.execute_sqls(insert_data) \n\n    # delete 80% of the rows\n    delete_nrows=int(nrows*0.8)\n    vacuum=f'delete from {table_name} where id < {delete_nrows};'\n    db.execute_sqls(vacuum)\n\n    # do the select , then the vacuum occurs\n    select='select * from '+table_name+' where id='\n    db.concurrent_execute_sql(threads,duration,select,nrows)\n\n    #delete the table\n    delete_table(table_name)\n    print_time()\n\n\nif __name__ == \"__main__\":\n    # Number of threads to use for concurrent inserts\n    num_threads = 176\n    \n    # Duration for which to run the inserts (in seconds)\n    insert_duration = None\n    \n    # Number of columns in the table\n    num_columns = 18\n    \n    # Number of rows to insert\n    num_rows = 3167807\n    \n    # Size of each column (in characters)\n    column_size = 70\n    \n    # Table name\n    table_name = 'table1'\n    \n    # Call the insert_large_data function\n    vacuum(num_threads, insert_duration, num_columns, num_rows, column_size, table_name)\n", "description": "In an online marketplace, 176 users perform simultaneous searches on a database table containing 18 columns and 3,167,807 rows of product records. Each column has a size of 70 characters. The searches are conducted after a large-scale data cleaning operation, which causes a database exception due to increased workload and decreased performance.\n", "workload": {"delete from table1 where id < 2534245;": 176}, "slow_queries": [], "exceptions": {"cpu": {"node_procs_running": [1.0, 3.0, 9.0, 2.0, 4.0, 8.0, 4.0, 11.0, 1.0, 5.0, 4.0, 4.0, 2.0, 1.0, 2.0, 4.0, 3.0, 5.0, 6.0, 1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 136.0, 186.0, 186.0, 186.0, 188.0, 186.0, 188.0, 191.0, 185.0, 182.0, 183.0, 186.0, 187.0, 194.0, 186.0, 188.0, 189.0, 185.0, 187.0, 187.0, 184.0, 183.0, 182.0, 182.0, 171.0, 168.0, 104.0, 53.0, 1.0], "node_procs_blocked": [0.0, 0.0, 0.0, 2.0, 1.0, 0.0, 0.0, 3.0, 2.0, 0.0, 0.0, 2.0, 1.0, 1.0, 3.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 2.0, 2.0, 1.0, 3.0, 3.0, 1.0, 1.0, 1.0, 0.0, 0.0, 2.0, 1.0, 0.0, 2.0, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 3.0, 0.0, 0.0, 2.0, 4.0, 0.0, 0.0, 1.0, 0.0], "node_entropy_available_bits": [3501.0, 3502.0, 3513.0, 3539.0, 3559.0, 3578.0, 3597.0, 3619.0, 3639.0, 3653.0, 3680.0, 3700.0, 3716.0, 3741.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0], "node_load1": [2.13, 1.96, 1.88, 1.88, 1.89, 1.89, 1.82, 1.91, 1.91, 1.84, 1.84, 1.93, 2.02, 2.02, 2.1, 2.1, 2.01, 1.93, 1.93, 1.93, 1.93, 1.86, 1.87, 1.87, 1.8, 1.8, 1.82, 1.75, 1.75, 1.69, 1.69, 1.56, 16.25, 16.25, 29.84, 29.84, 42.18, 53.7, 53.7, 64.06, 64.06, 73.42, 82.44, 82.44, 90.81, 90.81, 98.43, 105.6, 105.6, 111.88, 111.88, 117.66, 122.89, 122.89, 127.54, 127.54, 131.02, 129.18, 129.18, 118.83]}, "io": {"node_filesystem_size_bytes": [212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0], "irate(node_disk_reads_completed_total": [0.0, 0.0, 0.0, 0.0, 0.0, 0.6666666666666666, 0.0, 0.3333333333333333, 0.0, 0.6666666666666666, 36.333333333333336, 30.333333333333332, 1.0, 0.6666666666666666, 1.0, 109.66666666666667, 47.333333333333336, 14.333333333333334, 47.333333333333336, 205.66666666666666, 27.333333333333332, 49.666666666666664, 1.3333333333333333, 135.0, 37.666666666666664, 78.0, 59.666666666666664, 229.0, 2.0, 46.666666666666664, 195.33333333333334, 390.0, 50.666666666666664, 62.333333333333336, 17.0, 29.666666666666668, 0.3333333333333333, 0.0, 41.0, 9.333333333333334, 1.0, 0.3333333333333333, 2.0, 12.0, 15.0, 2.6666666666666665, 22.0, 0.0, 15.0, 0.0, 0.0, 59.666666666666664, 3.3333333333333335, 0.0, 0.6666666666666666, 26.0, 38.0, 21.666666666666668, 17.333333333333332, 12.0], "node_disk_io_now": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_disk_read_bytes_total": [0.0, 0.0, 0.0, 0.0, 0.0, 2730.6666666666665, 0.0, 1365.3333333333333, 0.0, 2730.6666666666665, 660821.3333333334, 703146.6666666666, 4096.0, 2730.6666666666665, 4096.0, 2323797.3333333335, 946176.0, 88746.66666666667, 233472.0, 6063445.333333333, 897024.0, 795989.3333333334, 13653.333333333334, 2514944.0, 516096.0, 2609152.0, 1981098.6666666667, 6425258.666666667, 87381.33333333333, 2348373.3333333335, 954368.0, 5540522.666666667, 1325738.6666666667, 2715648.0, 420522.6666666667, 1077248.0, 1365.3333333333333, 0.0, 1078613.3333333333, 518826.6666666667, 4096.0, 1365.3333333333333, 53248.0, 98304.0, 578901.3333333334, 98304.0, 253952.0, 0.0, 595285.3333333334, 0.0, 0.0, 248490.66666666666, 13653.333333333334, 0.0, 2730.6666666666665, 830122.6666666666, 1008981.3333333334, 1396736.0, 398677.3333333333, 49152.0], "irate(node_disk_read_time_seconds_total": [0.0, 0.0, 0.0, 0.0, 0.0, 0.012999999996585151, 0.0, 0.0, 0.0, 0.0016666666682188709, 0.11533333330104749, 0.09500000001086543, 0.018000000001241762, 0.007666666681567828, 0.029333333329608042, 0.9933333333271245, 1.3566666666495923, 0.15666666669615856, 0.10333333331315468, 1.1923333333494763, 0.4236666666499029, 0.9610000000102445, 0.050000000007761024, 1.0239999999757856, 1.172000000020489, 2.8009999999776483, 2.021999999997206, 4.542333333364998, 0.0803333333072563, 0.2616666666775321, 0.0636666666638727, 0.78666666666201, 0.05100000001645336, 0.11700000000807147, 0.02999999998913457, 0.1083333333178113, 0.0, 0.0, 0.06300000000434618, 0.029333333329608042, 0.0, 0.0003333333491658171, 0.0, 0.006000000013348957, 0.045999999972991645, 0.0006666666595265269, 0.021000000027318794, 0.0, 0.03899999998975545, 0.0, 0.0, 0.04233333332619319, 0.0006666666595265269, 0.0, 0.0, 0.06900000001769513, 0.03733333332153658, 0.0613333333361273, 0.034999999993791185, 0.03200000000651926]}, "memory": {"node_memory_Inactive_anon_bytes": [233639936.0, 233639936.0, 297340928.0, 645787648.0, 858333184.0, 994586624.0, 1410961408.0, 1609715712.0, 1681817600.0, 2072154112.0, 2288951296.0, 2443890688.0, 2763046912.0, 2976579584.0, 3170316288.0, 3441963008.0, 3714469888.0, 3863355392.0, 4073107456.0, 4132630528.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 4132626432.0, 212324352.0, 212324352.0, 280133632.0, 280129536.0, 280129536.0, 280129536.0, 280129536.0, 280137728.0, 280137728.0, 280039424.0, 279678976.0, 279707648.0, 279703552.0, 279965696.0, 279883776.0, 279932928.0, 280076288.0, 280084480.0, 280084480.0, 280072192.0, 280080384.0, 279777280.0, 279707648.0, 279719936.0, 278269952.0, 274472960.0, 267939840.0, 255156224.0, 241369088.0, 220807168.0, 212353024.0]}, "network": {"node_sockstat_TCP_tw": [9.0, 9.0, 9.0, 9.0, 9.0, 8.0, 8.0, 8.0, 7.0, 7.0, 5.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 6.0, 6.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 5.0, 8.0, 8.0, 7.0, 7.0, 10.0, 10.0, 10.0, 9.0, 10.0, 10.0, 10.0, 10.0, 12.0, 14.0, 14.0, 14.0, 14.0, 13.0, 13.0, 10.0, 10.0, 11.0, 11.0, 11.0, 11.0, 12.0, 12.0, 11.0, 12.0], "node_sockstat_TCP_orphan": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_netstat_Tcp_PassiveOpens": [0.3333333333333333, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.3333333333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 59.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.6666666666666666, 0.3333333333333333, 0.3333333333333333, 0.3333333333333333, 1.0, 1.0, 0.0, 0.0, 0.0, 0.3333333333333333, 0.0, 0.0, 0.6666666666666666, 0.3333333333333333, 0.3333333333333333, 0.3333333333333333, 0.6666666666666666, 0.3333333333333333, 0.0, 0.0, 0.0], "node_sockstat_TCP_alloc": [21.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 22.0, 21.0, 21.0, 197.0, 197.0, 197.0, 197.0, 197.0, 197.0, 197.0, 197.0, 199.0, 200.0, 201.0, 202.0, 200.0, 197.0, 197.0, 198.0, 198.0, 198.0, 198.0, 198.0, 200.0, 202.0, 201.0, 202.0, 202.0, 176.0, 122.0, 70.0, 21.0], "node_sockstat_TCP_inuse": [12.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 12.0, 12.0, 188.0, 188.0, 188.0, 188.0, 188.0, 188.0, 188.0, 188.0, 190.0, 190.0, 190.0, 190.0, 188.0, 188.0, 188.0, 189.0, 189.0, 189.0, 189.0, 189.0, 191.0, 192.0, 190.0, 190.0, 190.0, 167.0, 113.0, 61.0, 12.0]}}}