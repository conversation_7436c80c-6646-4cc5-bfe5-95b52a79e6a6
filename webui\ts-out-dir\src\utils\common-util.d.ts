declare const _default: {
    getWeek(): string;
    mobilePhone(str: any): boolean;
    toSplitNumFor(num: any, numToSpace: any): any;
    bankCardNo(str: any): boolean;
    regEmail(str: any): boolean;
    idCardNumber(str: any): boolean;
    deleteArrItem(arr: any, arrItem: any): void;
    arrToRepeat(arr: any): any;
    deRepeatArr(seriesArr: any): unknown[];
    byArrObjDeleteArrObj2(arrObj: any, arrObj2: any, objKey: any): any;
    deleteArrObjByKey(arrObj: any, objKey: any, value: any): any;
    findArrObjByKey(arrObj: any, objKey: any, value: any): any;
    byArrObjFindArrObj2(arrObj: any, arrObj2: any, objKey: any): any[];
};
export default _default;
