/*var mixin*/
@use 'sass:map';

@use 'config';
@use 'function' as *;

// set css var value, because we need translate value to string
// for example:
// @include set-css-var-value(('color', 'primary'), red);
// --el-color: red;
// --el-$name-: $value;
@mixin set-css-var-value($name, $value) {
  #{joinVarName($name)}: #{$value};
}

@mixin set-css-color-type($colors, $type) {
  @include set-css-var-value(('color', $type), map.get($colors, $type, 'base'));
  @each $i in (3, 5, 7, 8, 9) {
    // --el-color-primary-light-7: #c6e2ff;
    @include set-css-var-value(('color', $type, 'light', $i), map.get($colors, $type, 'light-#{$i}'));
  }

  //@include set-css-var-value(
  //      ('color', $type, 'dark-2'),
  //    map.get($colors, $type, 'dark-2')
  //);
}

//el-$name-$attribute-$value
@mixin set-component-css-var($name, $variables) {
  @each $attribute, $value in $variables {
    @if $attribute == 'default' {
      #{getCssVarName($name)}: #{$value};
    } @else {
      #{getCssVarName($name, $attribute)}: #{$value};
    }
  }
}

// --el-color-error-rgb: 245, 108, 108;
// --el-color-$type-rgb: 245, 108, 108;
@mixin set-css-color-rgb($colors, $type) {
  $color: map.get($colors, $type, 'base');
  @include set-css-var-value(('color', $type, 'rgb'), #{red($color), green($color), blue($color)});
}

// generate css var from existing css var
// for example:
// @include css-var-from-global(('button', 'text-color'), ('color', $type))
// --el-button-text-color: var(--el-color-#{$type});
@mixin css-var-from-global($var, $gVar) {
  $varName: joinVarName($var);
  $gVarName: joinVarName($gVar);
  #{$varName}: var(#{$gVarName});
}
