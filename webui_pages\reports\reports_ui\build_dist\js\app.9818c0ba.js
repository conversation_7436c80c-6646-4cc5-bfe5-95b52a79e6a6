(function(e){function t(t){for(var i,a,o=t[0],l=t[1],c=t[2],d=0,u=[];d<o.length;d++)a=o[d],Object.prototype.hasOwnProperty.call(n,a)&&n[a]&&u.push(n[a][0]),n[a]=0;for(i in l)Object.prototype.hasOwnProperty.call(l,i)&&(e[i]=l[i]);p&&p(t);while(u.length)u.shift()();return r.push.apply(r,c||[]),s()}function s(){for(var e,t=0;t<r.length;t++){for(var s=r[t],i=!0,o=1;o<s.length;o++){var l=s[o];0!==n[l]&&(i=!1)}i&&(r.splice(t--,1),e=a(a.s=s[0]))}return e}var i={},n={app:0},r=[];function a(t){if(i[t])return i[t].exports;var s=i[t]={i:t,l:!1,exports:{}};return e[t].call(s.exports,s,s.exports,a),s.l=!0,s.exports}a.m=e,a.c=i,a.d=function(e,t,s){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:s})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(a.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)a.d(s,i,function(t){return e[t]}.bind(null,i));return s},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],l=o.push.bind(o);o.push=t,o=o.slice();for(var c=0;c<o.length;c++)t(o[c]);var p=l;r.push([0,"chunk-vendors"]),s()})({0:function(e,t,s){e.exports=s("cd49")},"034f":function(e,t,s){"use strict";var i=s("85ec"),n=s.n(i);n.a},"0543":function(e,t,s){e.exports=s.p+"img/mem_robot.44e5fdbc.webp"},"0994":function(e,t,s){"use strict";var i=s("74a0"),n=s.n(i);n.a},"1ccc":function(e,t,s){},"28c5":function(e,t,s){"use strict";var i=s("50d1"),n=s.n(i);n.a},"2a70":function(e,t,s){},4263:function(e,t){e.exports="data:image/png;base64,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"},"44b9":function(e,t,s){"use strict";var i=s("cb20"),n=s.n(i);n.a},"462a":function(e,t,s){e.exports=s.p+"img/workload_robot.a4663857.webp"},"50d1":function(e,t,s){},"5cfa":function(e,t,s){e.exports=s.p+"img/index_robot.871d66b4.webp"},"5d2d":function(e,t,s){"use strict";var i=s("1ccc"),n=s.n(i);n.a},6004:function(e,t,s){e.exports=s.p+"img/query_robot.3342da36.webp"},6069:function(e,t,s){e.exports=s.p+"img/io_robot.1c748e65.webp"},7039:function(e,t,s){"use strict";var i=s("b5f7"),n=s.n(i);n.a},"70be":function(e,t,s){"use strict";var i=s("ce17"),n=s.n(i);n.a},"74a0":function(e,t,s){},"85ec":function(e,t,s){},"9f0b":function(e,t,s){e.exports=s.p+"img/cpu_robot.4c891857.webp"},a68a:function(e,t,s){},b20f:function(e,t,s){e.exports={menuText:"#999",menuActiveText:"#41b584",subMenuActiveText:"#41b584",menuBg:"#fff",menuHover:"#ecf8f3",subMenuBg:"#fff",subMenuHover:"#ecf8f3"}},b5f7:function(e,t,s){},b650:function(e,t,s){},cb20:function(e,t,s){},cd49:function(e,t,s){"use strict";s.r(t);s("e260"),s("e6cf"),s("cca6"),s("a79d");var i=s("2b0e"),n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{attrs:{id:"app"}},[s("WithStreamlitConnection",{scopedSlots:e._u([{key:"default",fn:function(e){var t=e.args;return[s("ReportComponent",{attrs:{args:t.args||{}}})]}}])})],1)},r=[],a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"c-flex-row",staticStyle:{width:"100%","font-size":"1rem","line-height":"1.6!important",height:"1000px"}},[i("div",{staticClass:"c-flex-column",staticStyle:{width:"55%"}},[i("div",{staticClass:"c-flex-row c-align-items-center c-justify-content-between c-shaow-card",staticStyle:{padding:"10px 20px","border-radius":"80px!important"}},[i("el-form",{ref:"form",attrs:{inline:!0,"label-position":"left"}},[i("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:e.$t("modelTip")+":"}},[i("el-select",{attrs:{placeholder:""},on:{change:e.getAlertHistories},model:{value:e.model,callback:function(t){e.model=t},expression:"model"}},e._l(e.modelList,(function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),i("div",{staticClass:"c-flex-row c-align-items-center",staticStyle:{"flex-shrink":"0"}},[i("el-switch",{staticStyle:{display:"block"},attrs:{"active-color":"#ff4949","inactive-color":"#13ce66","active-text":"","inactive-text":e.$t("playbackAnimationTip")},model:{value:e.skipTyped,callback:function(t){e.skipTyped=t},expression:"skipTyped"}}),i("div",{staticClass:"c-flex-row",staticStyle:{"margin-left":"20px"},on:{click:e.onEnClick}},[i("img",{staticStyle:{width:"30px",height:"30px"},attrs:{src:s("4263")}})])],1)],1),i("div",{staticClass:"c-flex c-flex-column",staticStyle:{height:"calc(100vh - 100px)","overflow-y":"auto",margin:"10px 0",padding:"0 10px"}},e._l(e.historyMessages,(function(t,s){return i("div",{key:s,staticClass:"diagnose-item c-flex-column",staticStyle:{background:"RGBA(255, 255, 255, 1.00)"}},[i("div",{staticClass:"c-flex-row c-align-items-center c-justify-content-between"},[i("div",{staticClass:"c-flex-row c-align-items-center c-justify-content-left"},[e._l(t.alerts,(function(t,s){return i("div",{key:s,staticClass:"c-flex-row c-justify-content-left",staticStyle:{"margin-right":"10px"}},[i("div",{style:e.severityStyle[t.alert_level]},[e._v(" ["+e._s(t.alert_level)+e._s("INFO"!==t.alert_level?"🔥":"")+"] ")]),i("div",{staticStyle:{color:"#666666","margin-left":"5px"}},[e._v(e._s(t.alert_name))])])})),i("div",{staticStyle:{color:"#999999","font-size":"12px",height:"12px","line-height":"12px","margin-top":"4px"}},[e._v(e._s(t.report_generate_time))])],2),i("div",{staticClass:"c-flex-row c-align-items-center"},[i("el-button",{staticStyle:{margin:"0 10px"},attrs:{type:"success",size:"small"},on:{click:function(s){return e.onReviewClick(t)}}},[e._v(e._s(e.$t("playbackButton"))),i("i",{staticClass:"el-icon-video-camera-solid el-icon--right",staticStyle:{"font-size":"16px"}})]),i("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(i){return e.onReportClick(t,s)}}},[e._v(e._s(e.$t("reportButton"))),i("i",{staticClass:"el-icon-document-add el-icon--right",staticStyle:{"font-size":"16px"}})])],1)])])})),0)]),i("div",{staticClass:"c-relative c-flex-column",staticStyle:{"overflow-y":"scroll",height:"100vh","overflow-x":"hidden",width:"45%",background:"RGBA(255, 255, 255, 1.00)"}},[i("div",{staticStyle:{"background-color":"white",padding:"0 10px",margin:"0 10px","border-radius":"8px"},domProps:{innerHTML:e._s(e.openReport)}})]),e.reviewDrawer?i("el-drawer",{directives:[{name:"loading",rawName:"v-loading",value:e.reviewLoading,expression:"reviewLoading"}],attrs:{title:e.$t("reviewDrawerTitle"),visible:e.reviewDrawer,size:"95vw","destroy-on-close":"",direction:"rtl"},on:{"update:visible":function(t){e.reviewDrawer=t}}},[i("div",{staticClass:"c-relative c-flex-column",staticStyle:{overflow:"hidden",height:"100%"}},[i("el-steps",{staticStyle:{width:"100%"},attrs:{active:e.activeName,"finish-status":"success",simple:""}},[i("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.$t("setpTitle1")},nativeOn:{click:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"n",void 0,t.key,void 0)?null:e.onStepClick(0)}}}),i("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.$t("setpTitle2")},nativeOn:{click:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"n",void 0,t.key,void 0)?null:e.onStepClick(1)}}}),i("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.$t("setpTitle3")},nativeOn:{click:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"n",void 0,t.key,void 0)?null:e.onStepClick(2)}}})],1),i("transition",{attrs:{name:"fade"}},[i("div",{ref:"setpScrollDiv",staticClass:"c-relative c-flex-column",staticStyle:{height:"calc(199 - 60px)","overflow-y":"auto",margin:"10px 0"},on:{scroll:e.stepScrollEvent}},[i("div",{staticClass:"review-step"},[i("div",{staticStyle:{height:"40px","line-height":"40px",color:"#333333","font-weight":"bold","font-size":"18px"}},[e._v(" 1."+e._s(e.$t("setpTip1"))+" ")]),i("div",{staticClass:"c-flex-row c-align-items-center",staticStyle:{height:"calc(100% - 40px)"}},[e.roleAssignerMessages.length>0?i("OneChat",{key:"RoleAssigner",staticClass:"chat-container",staticStyle:{height:"100%",width:"30%"},attrs:{id:"RoleAssigner",sender:"RoleAssigner","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.roleAssignerMessages},on:{playbackComplete:function(t){return e.onRoleAssignerPlaybackComplete("0")}}}):e._e(),i("div",{staticClass:"c-flex-row",staticStyle:{width:"70%",height:"100%"}},[e.cpuExpertMessages.length>0?i("OneChat",{key:"CpuExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"CpuExpert",sender:"CpuExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.cpuExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.ioExpertMessages.length>0?i("OneChat",{key:"IoExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"IoExpert",sender:"IoExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.ioExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.memoryExpertMessages.length>0?i("OneChat",{key:"MemoryExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"MemoryExpert",sender:"MemoryExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.memoryExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.indexExpertMessages.length>0?i("OneChat",{key:"IndexExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"IndexExpert",sender:"IndexExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.indexExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.configurationExpertMessages.length>0?i("OneChat",{key:"ConfigurationExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"ConfigurationExpert",sender:"ConfigurationExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.configurationExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.queryExpertMessages.length>0?i("OneChat",{key:"QueryExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"QueryExpert",sender:"QueryExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.queryExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e(),e.workloadExpertMessages.length>0?i("OneChat",{key:"WorkloadExpert",staticClass:"chat-container",staticStyle:{height:"100%","margin-left":"20px",flex:"1 1 50%"},attrs:{id:"WorkloadExpert",sender:"WorkloadExpert","type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.workloadExpertMessages},on:{playbackComplete:function(t){return e.onPlaybackComplete(1)}}}):e._e()],1)],1)]),i("div",{staticClass:"review-step"},[i("span",{staticStyle:{height:"40px","line-height":"40px",color:"#333333","font-weight":"bold",margin:"10px 0","font-size":"18px"}},[e._v(" 2."+e._s(e.$t("setpTip2"))+" ")]),e.brainstormingMessages.length>0?i("Chat",{staticClass:"chat-container",staticStyle:{height:"calc(100% - 40px)",width:"100%",padding:"0"},attrs:{"type-speed":e.typeSpeed,"skip-typed":e.skipTyped,messages:e.brainstormingMessages},on:{playbackComplete:function(t){return e.onBrainstormingPlaybackComplete()}}}):e._e()],1),i("div",{staticClass:"review-step"},[i("span",{staticStyle:{height:"40px","line-height":"40px",color:"#333333","font-weight":"bold","font-size":"18px"}},[e._v("3."+e._s(e.$t("setpTip3")))]),i("div",{staticStyle:{width:"100%",padding:"10px","background-color":"RGBA(242, 246, 255, 1)","border-radius":"8px"},domProps:{innerHTML:e._s(e.report)}})])])])],1)]):e._e()],1)},o=[],l=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"c-flex-column c-relative",staticStyle:{width:"100%",height:"100%"}},[s("div",{staticClass:"scroll-container c-relative",attrs:{id:e.componentId+"-scroll-container"}})])},c=[],p=(s("99af"),s("4160"),s("a9e3"),s("d3b7"),s("25f0"),s("498a"),s("159b"),s("d4cd")),d=s.n(p),u=s("1487"),g=s.n(u),h=s("1ac0"),m={name:"Chat",components:{},props:{messages:{type:Array,required:!0,default:function(){return[]}},skipTyped:{type:Boolean,default:!1},typeSpeed:{type:Number,default:100}},data:function(){return{typedObjs:[],chatText:"",componentId:Math.random().toString(36).substr(2,9),faceMap:{RoleAssigner:s("fbcc"),CpuExpert:s("9f0b"),MemoryExpert:s("0543"),IoExpert:s("6069"),IndexExpert:s("5cfa"),ConfigurationExpert:s("dab1"),QueryExpert:s("6004"),WorkloadExpert:s("462a")},scrollObserver:void 0,phraseVisible:!1,md:(new d.a).set({html:!0,breaks:!0,typographer:!0,linkify:!0}).set({highlight:function(e){return'<pre class="hljs"><code>'+g.a.highlight(e,{language:"python",ignoreIllegals:!0}).value+"</code></pre>"}})}},watch:{messages:{handler:function(){var e=this;setTimeout((function(){e.dealMessage(0)}))},deep:!0,immediate:!0}},destroyed:function(){this.scrollObserver&&(this.scrollObserver.disconnect(),this.scrollObserver=void 0),this.typedObjs.forEach((function(e){e.destroy()}))},mounted:function(){var e=document.getElementById(this.componentId+"-scroll-container");this.scrollObserver=new MutationObserver((function(){e.scrollTop=e.scrollHeight-e.clientHeight}));var t={attributes:!0,childList:!0,subtree:!0};this.scrollObserver.observe(e,t)},methods:{dealMessage:function(e){var t=this;if(e>=this.messages.length)return this.$emit("playbackComplete"),void(this.scrollObserver&&(this.scrollObserver.disconnect(),this.scrollObserver=void 0));var s=this.messages[e];if(s.data&&0!==s.data.trim().length){var i=this.faceMap[s.sender],n="message-"+Math.random().toString(36).substr(2,9),r=document.getElementById(this.componentId+"-scroll-container");r.innerHTML=r.innerHTML+'<div class="text-item c-flex-row left">\n          <img src="'.concat(i,'" class="face">\n          <div class="c-flex-column">\n            <span style="font-size: 1rem; color: #333333; margin-bottom: 5px">\n              ').concat(s.sender,'\n              <span style="margin-left: 5px; color: #666666">').concat(s.time,'</span>\n            </span>\n            <div id="').concat(n,'" class="content c-flex-column"></div>\n          </div>\n        </div>'),setTimeout((function(){try{if(t.skipTyped){var i=document.getElementById(n);i.innerHTML=t.md.render(s.data),t.dealMessage(e+1)}else{var r=new h["a"]("#"+n,{strings:[t.md.render(s.data)],typeSpeed:100-t.typeSpeed,showCursor:!1,contentType:"html",onComplete:function(){t.dealMessage(e+1)}});t.typedObjs.push(r)}}catch(a){console.log("Typed Error",a)}}),0)}else this.dealMessage(e+1)},onPhraseItemClick:function(e){this.chatText=e,this.phraseVisible=!1,this.$refs["chatInput"]&&this.$refs.chatInput.focus()},onChatConfirm:function(){var e=this.chatText;e&&""!==e.replaceAll(" ","")||this.$onceMessage.info("不能发送空消息")}}},f=m,y=(s("44b9"),s("5d2d"),s("2877")),v=Object(y["a"])(f,l,c,!1,null,"becc3bae",null),x=v.exports,b=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"c-flex-column c-relative",staticStyle:{width:"100%",height:"100%"}},[i("div",{staticClass:"scroll-container c-relative",attrs:{id:e.componentId+"-scroll-container"}},[i("div",{staticClass:"message-header c-relative c-flex-row c-align-items-center",style:e.headerStyle[e.sender]},["RoleAssigner"===e.sender?i("img",{staticClass:"face",attrs:{src:s("fbcc")}}):e._e(),"CpuExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("9f0b")}}):e._e(),"MemoryExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("0543")}}):e._e(),"IoExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("6069")}}):e._e(),"IndexExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("5cfa")}}):e._e(),"ConfigurationExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("dab1")}}):e._e(),"QueryExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("6004")}}):e._e(),"WorkloadExpert"===e.sender?i("img",{staticClass:"face",attrs:{src:s("462a")}}):e._e(),i("span",{staticStyle:{"font-size":"16px",color:"#FFFFFF","margin-left":"10px"}},[e._v(e._s(e.sender))])])])])},C=[],E={name:"OneChat",components:{},props:{messages:{type:Array,required:!0,default:function(){return[]}},sender:{type:String,required:!0,default:""},skipTyped:{type:Boolean,default:!1},typeSpeed:{type:Number,default:100}},data:function(){return{headerStyle:{RoleAssigner:"background-color: #01B77E;",CpuExpert:"background-color: #0F2F5F;",MemoryExpert:"background-color: #FB9996;",IoExpert:"background-color: #7649af;",IndexExpert:"background-color: #ecb42b;",ConfigurationExpert:"background-color: #67C23A;",QueryExpert:"background-color: #FFC2E3;",WorkloadExpert:"background-color: #D51374;"},typedObjs:[],componentId:Math.random().toString(36).substr(2,9),chats:[],scrollObserver:void 0,md:(new d.a).set({html:!0,breaks:!0,typographer:!0,linkify:!0}).set({highlight:function(e){return'<pre class="hljs"><code>'+g.a.highlight(e,{language:"python",ignoreIllegals:!0}).value+"</code></pre>"}})}},watch:{messages:{handler:function(){var e=this;setTimeout((function(){e.messages.length>0&&e.dealMessage(0)}))},deep:!0,immediate:!0}},mounted:function(){var e=document.getElementById(this.componentId+"-scroll-container");this.scrollObserver=new MutationObserver((function(){e.scrollTop=e.scrollHeight-e.clientHeight}));var t={attributes:!0,childList:!0,subtree:!0};this.scrollObserver.observe(e,t)},destroyed:function(){this.scrollObserver&&(this.scrollObserver.disconnect(),this.scrollObserver=void 0),this.typedObjs.forEach((function(e){e.destroy()}))},methods:{dealMessage:function(e){var t=this;if(e>=this.messages.length)return this.$emit("playbackComplete"),void(this.scrollObserver&&(this.scrollObserver.disconnect(),this.scrollObserver=void 0));var s=this.messages[e];if(s.data&&s.data.trim()){var i="message-"+Math.random().toString(36).substr(2,9),n=document.getElementById(this.componentId+"-scroll-container");n.innerHTML=n.innerHTML+'<div class="text-item c-flex-column">\n            <span style="font-size: 1rem; color: #333333; margin-bottom: 5px">\n              <span style="margin-left: 5px; color: #666666">'.concat(s.time,'</span>\n            </span>\n            <div id="').concat(i,'" class="content c-flex-column"></div>\n        </div>'),setTimeout((function(){try{if(t.skipTyped){var n=document.getElementById(i);n.innerHTML=t.md.render(s.data),t.dealMessage(e+1)}else{var r=new h["a"]("#"+i,{strings:[t.md.render(s.data)],typeSpeed:100-t.typeSpeed,showCursor:!1,contentType:"html",onComplete:function(){t.dealMessage(e+1)}});t.typedObjs.push(r)}}catch(a){console.log("Typed Error",a)}}),0)}else this.dealMessage(e+1)}}},A=E,k=(s("28c5"),s("70be"),Object(y["a"])(A,b,C,!1,null,"bb906802",null)),w=k.exports,S=s("d092"),T={filters:{},props:["args"],components:{OneChat:w,Chat:x},data:function(){return{timeRange:[],messages:[],openReport:"",openIndex:-1,severityStyle:{CRIT:"color: #F56C6C;",WARN:"color: #E6A23C;",INFO:"color: #909399;"},md:void 0,expertCount:0,roleAssignerMessages:[],cpuExpertMessages:[],ioExpertMessages:[],memoryExpertMessages:[],indexExpertMessages:[],configurationExpertMessages:[],queryExpertMessages:[],workloadExpertMessages:[],brainstormingMessages:[],tableMessages:[],report:"",introMessage:[],historyMessages:[],historyLoading:!1,reviewDrawer:!1,reviewItem:{},reviewLoading:!1,activeName:0,analyseAt:void 0,skipTyped:!0,typeSpeed:100,charts:[],modelList:[],model:""}},computed:{},watch:{args:{handler:function(e,t){var s=this;console.log("args changed from:",t),console.log("args changed to:",e),e&&this.$nextTick((function(){s.modelList=e.modelList||[],s.model=e.currentModel||s.modelList[0],s.historyMessages=e.diagnoseHistories||[],s.historyMessages.length>0&&s.onReportClick(s.historyMessages[0],0)}))},deep:!0,immediate:!0}},mounted:function(){this.md=(new d.a).set({html:!0,breaks:!0,typographer:!0,linkify:!0}).set({highlight:function(e){return'<pre class="hljs"><code>'+g.a.highlight(e,{language:"python",ignoreIllegals:!0}).value+"</code></pre>"}})},methods:{onEnClick:function(){this.$i18n.locale="en"===this.$i18n.locale?"zh":"en",localStorage.setItem("LanguageSwitching",this.$i18n.locale)},getAlertHistories:function(){var e={model:this.model};S["a"].setComponentValue(e)},onReviewClick:function(e){this.reviewLoading=!0,this.activeName=0,this.reviewDrawer=!0,this.getAlertHistoryDetail(e)},onRoleAssignerPlaybackComplete:function(){var e,t,s,i,n,r,a,o,l,c,p,d,u,g;this.expertCount=0,this.cpuExpertMessages=(null===(e=this.reviewItem.anomalyAnalysis)||void 0===e||null===(t=e.CpuExpert)||void 0===t?void 0:t.messages)||[],this.ioExpertMessages=(null===(s=this.reviewItem.anomalyAnalysis)||void 0===s||null===(i=s.IoExpert)||void 0===i?void 0:i.messages)||[],this.memoryExpertMessages=(null===(n=this.reviewItem.anomalyAnalysis)||void 0===n||null===(r=n.MemoryExpert)||void 0===r?void 0:r.messages)||[],this.indexExpertMessages=(null===(a=this.reviewItem.anomalyAnalysis)||void 0===a||null===(o=a.IndexExpert)||void 0===o?void 0:o.messages)||[],this.configurationExpertMessages=(null===(l=this.reviewItem.anomalyAnalysis)||void 0===l||null===(c=l.ConfigurationExpert)||void 0===c?void 0:c.messages)||[],this.queryExpertMessages=(null===(p=this.reviewItem.anomalyAnalysis)||void 0===p||null===(d=p.QueryExpert)||void 0===d?void 0:d.messages)||[],this.workloadExpertMessages=(null===(u=this.reviewItem.anomalyAnalysis)||void 0===u||null===(g=u.WorkloadExpert)||void 0===g?void 0:g.messages)||[],this.expertCount+=this.cpuExpertMessages.length>0?1:0,this.expertCount+=this.ioExpertMessages.length>0?1:0,this.expertCount+=this.memoryExpertMessages.length>0?1:0,this.expertCount+=this.indexExpertMessages.length>0?1:0,this.expertCount+=this.configurationExpertMessages.length>0?1:0,this.expertCount+=this.queryExpertMessages.length>0?1:0,this.expertCount+=this.workloadExpertMessages.length>0?1:0},onPlaybackComplete:function(e){var t;(this.expertCount-=e,this.expertCount<=0)&&(this.brainstormingMessages=(null===(t=this.reviewItem.brainstorming)||void 0===t?void 0:t.messages)||[],this.onStepClick(1))},onBrainstormingPlaybackComplete:function(){this.onStepClick(2),this.report=this.md.render(this.reviewItem.report||"")},getAlertHistoryDetail:function(e){this.roleAssignerMessages=[],this.cpuExpertMessages=[],this.ioExpertMessages=[],this.memoryExpertMessages=[],this.indexExpertMessages=[],this.configurationExpertMessages=[],this.queryExpertMessages=[],this.workloadExpertMessages=[],this.brainstormingMessages=[],this.reviewItem=e,console.log(this.reviewItem),this.roleAssignerMessages=this.reviewItem.anomalyAnalysis.RoleAssigner.messages||[],this.reviewLoading=!1},onReportClick:function(e,t){this.openIndex=t,this.charts=[],this.getAlertHistoryDetail(e),this.openReport=this.md.render(this.reviewItem.report||"")},onStepClick:function(e){this.activeName=e;var t=this.$refs.setpScrollDiv.getBoundingClientRect().height;this.scrollToTopWithAnimation(t*this.activeName)},scrollToTopWithAnimation:function(e){var t=this;i["default"].nextTick((function(){setTimeout((function(){t.$refs["setpScrollDiv"]&&t.$refs.setpScrollDiv.scrollTo({top:e,behavior:"smooth"})}),0)}))},stepScrollEvent:function(){if(this.$refs["setpScrollDiv"]){var e=this.$refs.setpScrollDiv.getBoundingClientRect().height;this.activeName=parseInt(this.$refs.setpScrollDiv.scrollTop/e+.5)}}}},M=T,I=(s("df49"),s("7039"),Object(y["a"])(M,a,o,!1,null,"6f4f86a6",null)),O=I.exports,R=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[""!=e.componentError?s("div",[s("h1",{staticClass:"err__title"},[e._v("Component Error")]),s("div",{staticClass:"err__msg"},[e._v(e._s(e.componentError))])]):void 0!=e.renderData?e._t("default",null,{args:e.renderData.args,disabled:e.renderData.disabled}):e._e()],2)},_=[],j=i["default"].extend({name:"withStreamlitConnection",data:function(){return{renderData:void 0,componentError:""}},methods:{onRenderEvent:function(e){var t=e;this.renderData=t.detail,this.componentError=""}},mounted:function(){S["a"].events.addEventListener(S["a"].RENDER_EVENT,this.onRenderEvent),S["a"].setComponentReady(),S["a"].setFrameHeight()},updated:function(){S["a"].setFrameHeight()},destroyed:function(){S["a"].events.removeEventListener(S["a"].RENDER_EVENT,this.onRenderEvent)},errorCaptured:function(e){this.componentError=String(e)}}),B=j,D=(s("0994"),Object(y["a"])(B,R,_,!1,null,"17a60eb2",null)),z=D.exports,H={name:"App",components:{ReportComponent:O,WithStreamlitConnection:z}},W=H,L=(s("034f"),Object(y["a"])(W,n,r,!1,null,null,null)),N=L.exports,F=s("5c96"),Q=s.n(F),P=(s("0fae"),s("b20f"),s("3d5b"),s("b650"),s("2a70"),s("a925")),U={analysisButton:"分析",timeRangeTip:"请选择异常发生时间进行分析",timeTip:"选择异常发生时间",refreshTip:"自动刷新",queryTimeTip:"异常时间",modelTip:"诊断模型",instanceTip:"实例",reviewDrawerTitle:"回看分析过程",timeStartTip:"开始时间",timeEndTip:"结束时间",playbackButton:"回放",reportButton:"报告",setpTitle1:"异常分析",playbackAnimationTip:"回放动画",animationSpeedTip:"动画速度",setpTip1:"DBA收到异常提醒后，会针对该异常进行分析，进而分配任务给不同的同事，接收到任务的同事会先独立进行分析。",setpTitle2:"圆桌讨论",setpTip2:"接收任务的同事独立进行异常分析后，会加入群组，进行圆桌讨论。",setpTitle3:"报告展示",setpTip3:"圆桌讨论后，DBA会将讨论结果汇总，出具异常分析诊断报告。",reportDrawerTitle:"分析报告",timeRangeSelectTip:"请选择异常发生时间",modelUsePrefixTip:"诊断报告由",modelUseSuffixTip:"生成"},q={analysisButton:"Analysis",timeRangeTip:"Select the time when the exception occurs for analysis",timeTip:"Select Anomaly Time",refreshTip:"Auto Refresh",queryTimeTip:"Anomaly Time",modelTip:"Anomaly Model",instanceTip:"Instance",reviewDrawerTitle:"Analysis Process Review",timeStartTip:"Start Time",timeEndTip:"Start Time",reportDrawerTitle:"Analysis Report",playbackButton:"Playback",playbackAnimationTip:"Playback Animation",animationSpeedTip:"Animation Speed",reportButton:"Report",setpTitle1:"Anomaly Analysis",setpTip1:"After receiving an exception notification, the DBA analyzes the exception and assigns tasks to different colleagues. The colleagues who receive the task perform the analysis independently first.",setpTitle2:"Round Table Discussion",setpTip2:"After the colleagues receiving the task independently analyze the anomaly, they join the group for a roundtable discussion.",setpTitle3:"Presentation Of Report",setpTip3:"After the roundtable discussion, the DBA will summarize the discussion results and issue an exception analysis and diagnosis report.",timeRangeSelectTip:"Select the time period when an anomaly occurs",modelUsePrefixTip:"Reports are generated by ",modelUseSuffixTip:""};i["default"].use(P["a"]);var G=new P["a"]({locale:localStorage.getItem("LanguageSwitching")||"zh",messages:{zh:U,en:q}}),K=G;i["default"].use(Q.a),i["default"].config.productionTip=!1,new i["default"]({i18n:K,render:function(e){return e(N)}}).$mount("#app")},ce17:function(e,t,s){},dab1:function(e,t,s){e.exports=s.p+"img/configuration_robot.7b743a08.webp"},df49:function(e,t,s){"use strict";var i=s("a68a"),n=s.n(i);n.a},fbcc:function(e,t,s){e.exports=s.p+"img/dba_robot.f875048d.webp"}});
//# sourceMappingURL=app.9818c0ba.js.map