{"start_time": "1697353738", "end_time": "1697353799", "start_timestamp": "2023-10-15 15:08:58", "end_timestamp": "2023-10-15 15:09:59", "alerts": [{"receiver": "db-gpt", "status": "resolved", "alerts": [{"status": "resolved", "labels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "annotations": {"description": "node:ins:stdload1[ins=] = 1.69 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.69"}, "startsAt": "2023-10-15T15:09:49.467858611Z", "endsAt": "2023-10-15T15:12:49.467858611Z", "generatorURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9090/graph?g0.expr=node%3Ains%3Astdload1+%3E+1&g0.tab=1", "fingerprint": "ab4787213c7dd319"}], "groupLabels": {"alertname": "NodeLoadHigh"}, "commonLabels": {"alertname": "NodeLoadHigh", "category": "node", "instance": "************:9100", "job": "node", "level": "1", "severity": "WARN"}, "commonAnnotations": {"description": "node:ins:stdload1[ins=] = 1.69 > 100%\n", "summary": "WARN NodeLoadHigh @************:9100 1.69"}, "externalURL": "http://iZ2ze0ree1kf7ccu4p1vcyZ:9093", "version": "4", "groupKey": "{}:{alertname=\"NodeLoadHigh\"}", "truncatedAlerts": 0}], "labels": ["POOR JOIN PERFORMANCE", "CPU CONTENTION"], "command": "python anomaly_trigger/main.py --anomaly POOR_JOIN_PERFORMANCE,CPU_CONTENTION", "script": "import  os\nimport re\nimport time\n\nimport psycopg2\n\n\nREPEATCOUNT = 1\nTIMELOGPATH = str(int(time.time())) + \"_job_trigger_time_log.txt\"\nTIMELOG = open(TIMELOGPATH, 'w+')\n\n\nclass Database():\n\n    def __init__(self):\n        self.conn = None\n        self.conn = psycopg2.connect(database='imdbload',\n                                     user='xxxx',\n                                     password='xxxx',\n                                     host='xxxx',\n                                     port=5432)\n\n    def execute_sql(self, sql):\n        fail = 1\n        cur = self.conn.cursor()\n        i = 0\n        cnt = 3\n        while fail == 1 and i < cnt:\n            try:\n                fail = 0\n                cur.execute(sql)\n            except BaseException as error:\n                fail = 1\n                print(error)\n            res = []\n            if fail == 0:\n                res = cur.fetchall()\n            i = i + 1\n        if fail == 1:\n            # print(\"SQL Execution Fatal!!\", sql)\n            return 0, ''\n        elif fail == 0:\n            return 1, res\n\n\ndef all_sql_files():\n    res_path = \"{}/join-order-benchmark-master/\".format(\n        os.path.dirname(os.path.abspath(__file__)))\n    # all_file_list = list(filter(file_filter, os.listdir(res_path)))\n    # all_file_list = sorted(all_file_list, key=custom_sort)\n    all_file_list = [\n        '1a.sql', '1b.sql', '1c.sql', '1d.sql',\n        '2a.sql', '2b.sql', '2c.sql', '2d.sql',\n        '3a.sql', '3b.sql', '3c.sql',\n        '4a.sql', '4b.sql', '4c.sql',\n        '5a.sql', '5b.sql', '5c.sql',\n        '6a.sql', '6b.sql', '6c.sql', '6d.sql', '6e.sql', '6f.sql',\n        '7a.sql', '7b.sql', '7c.sql',\n        '8a.sql', '8b.sql', '8c.sql', '8d.sql',\n        '9a.sql', '9b.sql', '9c.sql', '9d.sql',\n        '10a.sql', '10b.sql', '10c.sql',\n        '11a.sql', '11b.sql', '11c.sql', '11d.sql',\n        '12a.sql', '12b.sql', '12c.sql',\n        '13a.sql', '13b.sql', '13c.sql', '13d.sql',\n        '14a.sql', '14b.sql', '14c.sql',\n        '15a.sql', '15b.sql', '15c.sql', '15d.sql',\n        '16a.sql', '16b.sql', '16c.sql', '16d.sql',\n        '17a.sql', '17b.sql', '17c.sql', '17d.sql', '17e.sql', '17f.sql',\n        '18a.sql', '18b.sql', '18c.sql',\n        '19a.sql', '19b.sql', '19c.sql', '19d.sql',\n        '20a.sql', '20b.sql', '20c.sql',\n        '21a.sql', '21b.sql', '21c.sql',\n        '22a.sql', '22b.sql', '22c.sql', '22d.sql',\n        '23a.sql', '23b.sql', '23c.sql',\n        '24a.sql', '24b.sql',\n        '25a.sql', '25b.sql', '25c.sql',\n        '26a.sql', '26b.sql', '26c.sql',\n        '27a.sql', '27b.sql', '27c.sql',\n        '28a.sql', '28b.sql', '28c.sql',\n        '29a.sql', '29b.sql', '29c.sql',\n        '30a.sql', '30b.sql', '30c.sql',\n        '31a.sql', '31b.sql', '31c.sql',\n        '32a.sql', '32b.sql',\n        '33a.sql', '33b.sql', '33c.sql']\n\n    print(all_file_list)\n    files_list = []\n    for file in all_file_list:\n        files_list.append(res_path + file)\n    return files_list\n\n\ndef custom_sort(item):\n    # 提取数字和字母部分\n    match = re.match(r'(\\d+)(\\D+)', item)\n    # 将数字部分转换为整数以进行比较\n    num_part = int(match.group(1))\n    # 返回元组以按数字和字母排序\n    return (num_part, match.group(2))\n\n\ndef file_filter(f):\n    if f[-4:] == '.sql' and 'schema' not in f and 'fkindexes' not in f:\n        return True\n    else:\n        return False\n\n\ndef get_sql_from_file(file_name):\n    file = open(file_name)\n    lines = file.readlines().copy()\n    sql = ''\n    for line in lines:\n        sql += line\n    sql = sql.replace('\n', ' ').replace('   ', ' ').replace('  ', ' ')\n    file.close()\n    return sql\n\n\ndef test_hint_from_file(sql_file):\n    db = Database()\n    sql = get_sql_from_file(sql_file)\n    success, result_cont = db.execute_sql(sql)\n    print(success, result_cont)\n\n\ndef test_all():\n    sql_files = all_sql_files()\n\n    for sql_file in list(sql_files)[:-10]:\n        if sql_file:\n            test_hint_from_file(sql_file)\n\n\ndef test_one():\n    res_path = \"{}/join-order-benchmark-master/\".format(\n        os.path.dirname(os.path.abspath(__file__)))\n    test_hint_from_file(res_path + '1a.sql')\n\n\nif __name__ == '__main__':\n    for i in range(0, REPEATCOUNT):\n        TIMELOG.write(str(int(time.time()))+\";\")\n        test_all()\n        TIMELOG.write(str(int(time.time()))+\"\n\")\n        TIMELOG.flush()\n\n    TIMELOG.close()\n\n)\n", "description": "In an online database where multiple tasks are running simultaneously, there is a performance issue with joining tables in a database query. This is causing the CPU to become overloaded, leading to contention and slowing down the overall system performance.\n", "workload": {"SELECT MIN(mc.note) AS production_note, MIN(t.title) AS movie_title, MIN(t.production_year) AS movie_year FROM company_type AS ct, info_type AS it, movie_companies AS mc, movie_info_idx AS mi_idx, title AS t WHERE ct.kind = 'production companies' AND it.info = 'top 250 rank' AND mc.note NOT LIKE '%(as Metro-Goldwyn-Mayer Pictures)%' AND (mc.note LIKE '%(co-production)%' OR mc.note LIKE '%(presents)%') AND ct.id = mc.company_type_id AND t.id = mc.movie_id AND t.id = mi_idx.movie_id AND mc.movie_id = mi_idx.movie_id AND it.id = mi_idx.info_type_id;": 103}, "slow_queries": ["SELECT MIN(mc.note) AS production_note, MIN(t.title) AS movie_title, MIN(t.production_year) AS movie_year FROM company_type AS ct, info_type AS it, movie_companies AS mc, movie_info_idx AS mi_idx, title AS t WHERE ct.kind = 'production companies' AND it.info = 'top 250 rank' AND mc.note NOT LIKE '%(as Metro-Goldwyn-Mayer Pictures)%' AND (mc.note LIKE '%(co-production)%' OR mc.note LIKE '%(presents)%') AND ct.id = mc.company_type_id AND t.id = mc.movie_id AND t.id = mi_idx.movie_id AND mc.movie_id = mi_idx.movie_id AND it.id = mi_idx.info_type_id;"], "exceptions": {"cpu": {"node_procs_running": [1.0, 10.0, 8.0, 8.0, 6.0, 5.0, 3.0, 6.0, 6.0, 8.0, 5.0, 8.0, 5.0, 6.0, 5.0, 12.0, 10.0, 6.0, 5.0, 5.0, 9.0], "node_procs_blocked": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "node_entropy_available_bits": [3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0, 3754.0], "node_load1": [7.14, 7.14, 6.88, 6.65, 6.65, 6.6, 6.6, 6.39, 6.6, 6.6, 6.47, 6.47, 6.35, 6.25, 6.25, 6.15, 6.15, 5.89, 5.82, 5.82, 5.68]}, "io": {"node_filesystem_size_bytes": [212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0, 212300566528.0], "1-(node_filesystem_free_bytes": [0.44161838991435143, 0.44161910377019487, 0.44161910377019487, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996, 0.4416191809437996], "node_disk_io_now": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_disk_read_bytes_total": [0.0, 75826517.33333333, 0.0, 666282.6666666666, 0.0, 0.0, 1365.3333333333333, 0.0, 96938.66666666667, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_disk_read_time_seconds_total": [0.0, 2.9626666666784636, 0.0, 0.020333333328987162, 0.0, 0.0, 0.0, 0.0, 0.002333333327745398, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "memory": {"irate(node_disk_write_time_seconds_total": [0.0, 0.00033333331036070984, 0.00033333331036070984, 0.0016666667070239782, 0.0, 0.0, 0.0, 0.0, 0.00033333331036070984, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0006666666983316342, 0.0, 0.00033333331036070984, 0.0], "node_memory_Buffers_bytes": [18862080.0, 18882560.0, 18890752.0, 19046400.0, 19054592.0, 19054592.0, 19066880.0, 19066880.0, 19079168.0, 19079168.0, 19087360.0, 19087360.0, 19087360.0, 19095552.0, 19103744.0, 19103744.0, 19103744.0, 19103744.0, 19103744.0, 19111936.0, 19111936.0]}, "network": {"node_sockstat_TCP_tw": [7.0, 7.0, 8.0, 8.0, 8.0, 7.0, 7.0, 7.0, 8.0, 8.0, 7.0, 7.0, 7.0, 7.0, 7.0, 6.0, 6.0, 6.0, 5.0, 5.0, 5.0], "node_sockstat_TCP_orphan": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "irate(node_netstat_Tcp_PassiveOpens": [0.3333333333333333, 1.0, 1.6666666666666667, 2.0, 2.0, 2.0, 1.6666666666666667, 2.0, 2.3333333333333335, 1.6666666666666667, 2.0, 2.0, 2.0, 1.6666666666666667, 2.0, 2.0, 2.0, 1.6666666666666667, 2.0, 2.0, 2.3333333333333335], "node_sockstat_TCP_alloc": [22.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0, 23.0], "node_sockstat_TCP_inuse": [12.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0, 13.0]}}}