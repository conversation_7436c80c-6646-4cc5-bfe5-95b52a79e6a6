{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/node/title-content-node.vue?ec8d", "webpack:///./src/App.vue?7e02", "webpack:///./src/assets/mem_robot.webp", "webpack:///./src/streamlit/WithStreamlitConnection.vue?0067", "webpack:///./src/assets sync ^\\.\\/.*$", "webpack:///./src/report_flow.vue?4c8b", "webpack:///./src/coms/vue-endpoint.vue?07f3", "webpack:///./src/node/agent_group_node.vue?93b5", "webpack:///./src/coms/vue-node.vue?3e9b", "webpack:///./src/assets/workload_robot.webp", "webpack:///./src/chat/OneChat.vue?7db6", "webpack:///./src/coms/vue-group.vue?2137", "webpack:///./src/assets/index_robot.webp", "webpack:///./src/assets/query_robot.webp", "webpack:///./src/assets/io_robot.webp", "webpack:///./src/butterfly-vue.vue?13d8", "webpack:///./src/assets/net_robot.webp", "webpack:///./src/assets/cpu_robot.webp", "webpack:///./src/App.vue?40f7", "webpack:///./src/report_flow.vue?c2d5", "webpack:///./src/butterfly-vue.vue?4c9d", "webpack:///./src/util/default-data.js", "webpack:///./src/coms/node.js", "webpack:///./src/coms/tree-node.js", "webpack:///./src/coms/edge.js", "webpack:///./src/coms/group.js", "webpack:///./src/util/diff.js", "webpack:///./src/util/re-layout.js", "webpack:///./src/coms/vue-group.vue?5653", "webpack:///src/coms/vue-group.vue", "webpack:///./src/coms/vue-group.vue?69fc", "webpack:///./src/coms/vue-group.vue?84ed", "webpack:///./src/coms/vue-node.vue?325d", "webpack:///src/coms/vue-node.vue", "webpack:///./src/coms/vue-node.vue?460b", "webpack:///./src/coms/vue-node.vue?0b86", "webpack:///./src/util/add-com.js", "webpack:///./src/util/process.js", "webpack:///./src/util/re-calc.js", "webpack:///src/butterfly-vue.vue", "webpack:///./src/butterfly-vue.vue?f20d", "webpack:///./src/butterfly-vue.vue?7d8e", "webpack:///./src/coms/vue-endpoint.vue?38ee", "webpack:///src/coms/vue-endpoint.vue", "webpack:///./src/coms/vue-endpoint.vue?a06c", "webpack:///./src/coms/vue-endpoint.vue?8290", "webpack:///./src/node/title-content-node.vue?faff", "webpack:///src/node/title-content-node.vue", "webpack:///./src/node/title-content-node.vue?3f9c", "webpack:///./src/node/title-content-node.vue?b9c2", "webpack:///./src/node/agent_group_node.vue?e2c4", "webpack:///./src/chat/OneChat.vue?9024", "webpack:///src/chat/OneChat.vue", "webpack:///./src/chat/OneChat.vue?f080", "webpack:///./src/chat/OneChat.vue?2369", "webpack:///src/node/agent_group_node.vue", "webpack:///./src/node/agent_group_node.vue?ee18", "webpack:///./src/node/agent_group_node.vue?a1f8", "webpack:///./src/util/edge.js", "webpack:///src/report_flow.vue", "webpack:///./src/report_flow.vue?75ec", "webpack:///./src/report_flow.vue?5b47", "webpack:///./src/streamlit/WithStreamlitConnection.vue?267f", "webpack:///./src/streamlit/WithStreamlitConnection.vue?9478", "webpack:///./src/streamlit/WithStreamlitConnection.vue?64f6", "webpack:///./src/streamlit/WithStreamlitConnection.vue?f273", "webpack:///src/App.vue", "webpack:///./src/App.vue?1160", "webpack:///./src/App.vue?bff9", "webpack:///./src/main.ts", "webpack:///./src/assets/configuration_robot.webp", "webpack:///./src/chat/OneChat.vue?4b5e", "webpack:///./src/butterfly-vue.vue?a4cf", "webpack:///./src/assets/dba_robot.webp"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "map", "webpackContext", "req", "id", "webpackContextResolve", "e", "Error", "code", "keys", "resolve", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "scopedSlots", "_u", "fn", "ref", "args", "staticRenderFns", "staticClass", "style", "options", "nodeData", "on", "finishLoaded", "class", "className", "defaultOptions", "disLinkable", "linkable", "draggable", "zoomable", "moveable", "theme", "edge", "shapeType", "getNodeStyle", "left", "top", "position", "DefaultNode", "obj", "div", "document", "createElement", "Reflect", "ownKeys", "for<PERSON>ach", "Node", "DefaultTreeNode", "children", "parent", "collapsed", "isRoot", "TreeNode", "DefaultEdge", "isShow", "dom", "String", "Number", "Date", "Edge", "getGroupStyle", "DefaultGroup", "group", "Group", "adapterArray", "Array", "isArray", "diff", "news", "olds", "created", "deleted", "updated", "item", "includes", "canvas", "nodes", "relayout", "layout", "type", "_autoLayout", "resetCanvasNodesPosition", "constructor", "TreeCanvas", "autoLayout", "node", "moveTo", "recalc", "tempNodeObj", "_", "_v", "_s", "itemData", "props", "required", "component", "render", "vueCon", "canvasNodes", "<PERSON><PERSON>", "extend", "template", "VueGroup", "VueNode", "propsData", "canvasNodeIndex", "findIndex", "console", "warn", "canvasNode", "nodeCon", "$butterfly", "_events", "$mount", "Con", "addGroupsCom", "canvasRoot", "groups", "index", "querySelector", "groupCon", "append", "$el", "addNodesCom", "addEdgesCom", "edges", "edgeCon", "process", "BaseNode", "Class", "processNodes", "oldNodes", "previousIsFlatNode", "_handleTreeNodes", "removeNodes", "addNodes", "root", "getDataMap", "processEdge", "old<PERSON><PERSON>", "removeEdges", "addEdges", "processGroups", "oldGroups", "removeGroup", "addGroup", "endpoints", "isFunction", "updatePos", "redraw", "default", "baseCanvas", "Function", "canvasConf", "canvasData", "watch", "handler", "updateCavans", "re", "mounted", "initCanvas", "$emit", "dragGroup", "groupIndex", "dragNode", "nodeIndex", "methods", "log", "onCreateEdge", "link", "sourceEndpointId", "sourceEndpoint", "sourceNodeId", "sourceNode", "targetEndpointId", "targetEndpoint", "targetNodeId", "targetNode", "edgeInfo", "source", "target", "onDeleteEdge", "onChangeEdges", "addLinkData", "delLinkData", "addLink", "delLinks", "info", "onOtherEvent", "_g", "$listeners", "_t", "param", "findParent", "self", "$parent", "getEndpoint", "addEndpoint", "<PERSON><PERSON><PERSON><PERSON>", "butterflyParent", "removeEndpoint", "userData", "isCompleted", "title", "content", "_e", "components", "_l", "isRuning", "staticStyle", "messages", "isDiagnosing", "slot", "sender", "time", "domProps", "componentId", "Math", "random", "toString", "substr", "chats", "scrollObserver", "undefined", "md", "message", "trim", "deep", "immediate", "OneChat", "defaultExpertData", "expertData", "JSON", "parse", "stringify", "newVal", "expertItem", "oldVal", "BaseEdge", "path", "texts", "ButterflyVue", "width", "height", "background", "runData", "defaults", "canvansRef", "butterflyVue", "jsonData", "classMap", "addEdge", "mockData", "VueCom", "componentError", "renderData", "disabled", "onRenderEvent", "event", "renderEvent", "detail", "events", "addEventListener", "RENDER_EVENT", "setComponentReady", "setFrameHeight", "destroyed", "removeEventListener", "errorCaptured", "err", "config", "productionTip", "$ELEMENT", "size", "zIndex", "h", "App"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,yBAAyd,EAAG,G,oCCA5d,yBAAwb,EAAG,G,uBCA3bW,EAAOD,QAAU,IAA0B,+B,oCCA3C,yBAAsf,EAAG,G,qBCAzf,IAAI+B,EAAM,CACT,6BAA8B,OAC9B,mBAAoB,OACpB,mBAAoB,OACpB,qBAAsB,OACtB,kBAAmB,OACnB,mBAAoB,OACpB,mBAAoB,OACpB,qBAAsB,OACtB,wBAAyB,QAI1B,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOpC,EAAoBqC,GAE5B,SAASC,EAAsBF,GAC9B,IAAIpC,EAAoBW,EAAEuB,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAO5D,OAAO4D,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBlC,EAAOD,QAAUgC,EACjBA,EAAeE,GAAK,Q,oCC9BpB,yBAAgc,EAAG,G,2DCAnc,yBAA8d,EAAG,G,6DCAje,yBAAud,EAAG,G,oCCA1d,yBAAue,EAAG,G,yECA1ejC,EAAOD,QAAU,IAA0B,oC,kCCA3C,yBAA8c,EAAG,G,oCCAjd,yBAAwe,EAAG,G,uBCA3eC,EAAOD,QAAU,IAA0B,iC,qBCA3CC,EAAOD,QAAU,IAA0B,iC,qBCA3CC,EAAOD,QAAU,IAA0B,8B,kCCA3C,yBAAkc,EAAG,G,gDCArcC,EAAOD,QAAU,IAA0B,+B,yECA3CC,EAAOD,QAAU,IAA0B,+B,yJCAvC,EAAS,WAAa,IAAIyC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,0BAA0B,CAACG,YAAYP,EAAIQ,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAC5M,IAAIC,EAAOD,EAAIC,KACf,MAAO,CAACP,EAAG,aAAa,CAACE,MAAM,CAAC,KAAOK,aAAgB,IACnDC,EAAkB,GCHlB,EAAS,WAAa,IAAIZ,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,UAAUC,MAAQ,UAAad,EAAIe,QAAa,MAAI,aAAgBf,EAAIe,QAAc,OAAI,iBAAoBf,EAAIe,QAAkB,WAAI,KAAO,CAACX,EAAG,gBAAgB,CAACxB,IAAI,OAAO8B,IAAI,eAAeJ,MAAM,CAAC,WAAaN,EAAIgB,SAAS,UAAY,QAAQC,GAAG,CAAC,SAAWjB,EAAIkB,iBAAiB,IAC7Y,EAAkB,GCDlB,G,oBAAS,WAAa,IAAIlB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACe,MAAMnB,EAAIoB,WAAW,CAAChB,EAAG,MAAM,CAACM,IAAI,aAAaG,YAAY,gCACnK,EAAkB,G,sDCDhBQ,EAAiB,CACrBC,aAAa,EACbC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,MAAO,CACLC,KAAM,CACJC,UAAW,oB,gGCNXC,EAAe,SAACC,EAAMC,GAC1B,MAAO,CACLD,KAAMA,EAAO,KACbC,IAAKA,EAAM,KACXC,SAAU,aAIRC,E,6PAEG,SAACC,GACN,IAAMC,EAAMC,SAASC,cAAc,OAC7BxB,EAAQgB,EAAaK,EAAIJ,KAAMI,EAAIH,KAQzC,OAPAO,QAAQC,QAAQ1B,GAAO2B,SAAQ,SAAA7D,GAC7BwD,EAAItB,MAAMlC,GAAOkC,EAAMlC,MAGzBwD,EAAIhB,UAAY,iBAChBgB,EAAI3C,GAAK,WAAa0C,EAAI1C,GAEnB2C,K,YAZeM,WAiBXR,ICzBTJ,EAAe,SAACC,EAAMC,GAC1B,MAAO,CACLD,KAAMA,EAAO,KACbC,IAAKA,EAAM,KACXC,SAAU,aAIRU,E,wDACJ,WAAY5B,GAAS,oCACnB,cAAMA,GADa,yCAUd,SAACoB,GACN,IAAMC,EAAMC,SAASC,cAAc,OAC7BxB,EAAQgB,EAAaK,EAAIJ,KAAMI,EAAIH,KAQzC,OAPAO,QAAQC,QAAQ1B,GAAO2B,SAAQ,SAAA7D,GAC7BwD,EAAItB,MAAMlC,GAAOkC,EAAMlC,MAGzBwD,EAAIhB,UAAY,iBAChBgB,EAAI3C,GAAK,WAAa0C,EAAI1C,GAEnB2C,KAlBP,EAAKQ,SAAW7B,EAAQ6B,SACxB,EAAKC,OAAS9B,EAAQ8B,OACtB,EAAKC,UAAY/B,EAAQ+B,YAAa,EAClC/B,EAAQgC,SACV,EAAKA,OAAShC,EAAQgC,QANL,E,UADOC,eA0BfL,I,gDClCTM,E,oLACMC,GACR,IAAIC,EAAM,uEAAgBD,GAC1B,OAAOC,I,kCAGP,IAAMf,EAAMC,SAASC,cAAc,OASnC,OAPKrC,KAAKR,KACRQ,KAAKR,GAAK2D,OAAOC,OAAO,IAAIC,QAG9BlB,EAAI3C,GAAJ,qBAAuBQ,KAAKR,IAC5B2C,EAAIhB,UAAY,oBAETgB,M,GAfemB,WAmBXN,ICnBTO,G,UAAgB,SAACzB,EAAMC,GAC3B,MAAO,CACLD,KAAMA,EAAO,KACbC,IAAKA,EAAM,KACXC,SAAU,cAIRwB,E,+KACCC,GACH,IAAMtB,EAAMC,SAASC,cAAc,OAE7BxB,EAAQ0C,EAAcE,EAAM3B,KAAM2B,EAAM1B,KAU9C,OATA9F,OAAO4D,KAAKgB,GAAO2B,SAAQ,SAAA7D,GACzBwD,EAAItB,MAAMlC,GAAOkC,EAAMlC,MAGzBwD,EAAIhB,UAAY,kBAEhBgB,EAAI3C,GAAJ,mBAAqBiE,EAAMjE,IAC3B2C,EAAIhB,UAAY,oBAETgB,M,GAdgBuB,YAkBZF,I,kCC5BTG,EAAe,SAACtF,GACpB,OAAOuF,MAAMC,QAAQxF,GAASA,EAAQ,CAACA,IASnCyF,EAAO,WAAsC,IAArCC,EAAqC,uDAA9B,GAAIC,EAA0B,uDAAnB,GAAIrF,EAAe,uDAAT,KACxCoF,EAAOJ,EAAaI,GACpBC,EAAOL,EAAaK,GAEpB,IAJiD,EAI3CC,EAAU,GACVC,EAAU,GACVC,EAAU,GANiC,iBAQhCJ,GARgC,IAQjD,2BAAuB,KAAdK,EAAc,QAChBJ,EAAK3E,KAAI,SAAA+E,GAAI,OAAIA,EAAKzF,MAAM0F,SAASD,EAAKzF,IAG7CwF,EAAQ7H,KAAK8H,GAFbH,EAAQ3H,KAAK8H,IAVgC,qDAgBhCJ,GAhBgC,IAgBjD,2BAAuB,KAAdI,EAAc,QAChBL,EAAK1E,KAAI,SAAA+E,GAAI,OAAIA,EAAKzF,MAAM0F,SAASD,EAAKzF,KAC7CuF,EAAQ5H,KAAK8H,IAlBgC,8BAsBjD,MAAO,CAACH,UAASC,UAASC,YAGbL,I,qBChCA,WAACQ,GAAyB,IAAjBC,EAAiB,uDAAT,KAC9B,GAAKD,EAAL,CAGA,IAAKA,EAAOE,SAAU,OACIF,EAAOG,QAAU,GAAlCC,EADa,EACbA,KAAM5D,EADO,EACPA,QAEbwD,EAAOE,SAAW,SAAUD,GACtBA,GAEFD,EAAOK,YAAP,iCAAuBL,GAAvB,IAA+BC,WAG/BK,EAAyBN,EAAQC,IAE7BD,EAAOO,cAAgBC,gBACzBR,EAAOK,YAAYL,GACVI,GAAQ5D,GACjBwD,EAAOS,WAAWL,EAAM5D,GAAW,IAIvCd,KAAKuE,MAAM/B,SAAQ,SAAAwC,GACjBA,EAAKC,OAAOD,EAAKlD,KAAMkD,EAAKjD,QAG1BuC,EAAOY,QACTZ,EAAOY,UAIbZ,EAAOE,SAASD,KAGZK,EAA2B,SAACN,EAAQC,GACxC,IAAMY,EAAc,GAEpBZ,EAAM/B,SAAQ,SAAA4B,GACZe,EAAYf,EAAK5E,IAAM4E,KAGzBE,EAAOC,MAAM/B,SAAQ,SAAC4B,GAChBgB,IAAEnH,IAAIkH,EAAN,WAAsBf,EAAK5E,GAA3B,aACF4E,EAAKtC,KAAOqD,EAAYf,EAAK5E,IAAIsC,MAE/BsD,IAAEnH,IAAIkH,EAAN,WAAsBf,EAAK5E,GAA3B,YACF4E,EAAKrC,IAAMoD,EAAYf,EAAK5E,IAAIuC,S,YCjDlC,EAAS,WAAa,IAAIhC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,gBAAgB,CAACT,EAAG,MAAM,CAACS,YAAY,uBAAuB,CAACb,EAAIsF,GAAG,SAAStF,EAAIuF,GAAGvF,EAAIwF,SAAS/F,IAAI,OAAOW,EAAG,MAAM,CAACS,YAAY,4BACtP,EAAkB,GCUtB,GACEhD,KAAM,YACN4H,MAAO,CACLD,SAAU,CACRb,KAAMzI,OACNwJ,UAAU,KChBmU,I,wBCQ/UC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBX,EAAS,WAAa,IAAI3F,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,eAAe,CAACb,EAAIsF,GAAG,QAAQtF,EAAIuF,GAAGvF,EAAIwF,SAAS/F,IAAI,QACzK,EAAkB,GCOtB,GACE5B,KAAM,WACN4H,MAAO,CACLD,SAAU,CACRb,KAAMzI,OACNwJ,UAAU,KCbkU,ICQ9U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,K,QCPTE,GAAS,SAATA,EAAUvB,EAAMM,GAA4C,IAE5DkB,EAFsBhD,EAAsC,uDAA7B,KAAMiD,EAAuB,uDAAT,KAIvD,GAAIzB,EAAKuB,OACP,sBAAevB,EAAKuB,SAClB,IAAK,SACHC,EAASE,aAAIC,OAAO,CAClBC,SAAU5B,EAAKuB,OACfH,MAAM,CACJD,SAAU,CACRb,KAAMzI,OACNwJ,UAAU,MAIhB,MAEF,IAAK,SACHG,EAASE,aAAIC,OAAO3B,EAAKuB,QACzB,MAEF,QACE,MAAMhG,MAAM,GAAD,OAAI+E,EAAJ,4DAAoDiB,UAGnE,OAAQjB,GACN,IAAK,QACHkB,EAASE,aAAIC,OAAOE,GACpB,MAEF,IAAK,OACHL,EAASE,aAAIC,OAAOG,IACpB,MAEF,QACE,MAKN,IAAIC,EAAY,CACdZ,SAAUnB,GAGZ,GAAa,SAATM,EAAiB,CACnB,IAAI0B,EAAkBP,EAAYQ,WAAU,SAACrB,GAC3C,OAAOA,EAAKxF,KAAO4E,EAAK5E,MAG1B,IAAyB,IAArB4G,EAEF,OADAE,QAAQC,KAAR,uBACO,KAGT,IAAIC,EAAaX,EAAYO,GAE7BD,EAAUK,WAAaA,EAEvB,IAAMC,EAAU,IAAIb,EAAO,CACzBhD,OAAQA,EACRuD,cAWF,OATAM,EAAQC,WAAa,CACnBhC,KAAMA,GAKR+B,EAAQE,QAAU/D,EAAO+D,QACzBF,EAAQG,SAEDH,EAGP,IAAMI,EAAM,IAAIjB,EAAO,CACrBO,cAQF,OAHAU,EAAIF,QAAU/D,EAAO+D,QACrBE,EAAID,SAEGC,GAULC,GAAe,SAACC,EAAYC,EAAQpE,GACxCoE,EAAO3H,KAAI,SAAC+E,EAAK6C,GACf,IAAMzH,EAAK4E,EAAK5E,GAChB,GAAKA,EAAL,CAKA,IAAM0D,EAAM6D,EAAWG,cAAX,0BAA4C9C,EAAK5E,GAAjD,OAEZ,GAAK0D,EAAL,CAIA,IAAIiE,EAAWxB,GAAOvB,EAAM,QAASxB,GAErCM,EAAIkE,OAAOD,EAASE,WAZlBf,QAAQC,KAAR,iBAAuBU,EAAvB,qBAiBAK,GAAc,SAACP,EAAYlB,EAAatB,EAAO3B,GACnD2B,EAAMlF,KAAI,SAAC+E,EAAK6C,GACd,IAAI7B,IAAEvB,QAAQO,GAAd,CAGA,IAAM5E,EAAK4E,EAAK5E,GAChB,GAAKA,EAAL,CAKA,IAAM0D,EAAM6D,EAAWG,cAAX,yBAA2C9C,EAAK5E,GAAhD,OAEZ,GAAK0D,EAAL,CAIA,IAAIuD,EAAUd,GAAOvB,EAAM,OAAQxB,EAAQiD,GAE3C3C,EAAIkE,OAAOX,EAAQY,KAGnB,IAAIjB,EAAkBP,EAAYQ,WAAU,SAACrB,GAC3C,OAAOA,EAAKxF,KAAO4E,EAAK5E,MAG1B,OAAyB,IAArB4G,GACFE,QAAQC,KAAR,uBACO,WAFT,QAnBED,QAAQC,KAAR,gBAAsBU,EAAtB,sBA0BAM,GAAc,SAACR,EAAYS,EAAO5E,GACtC4E,EAAMnI,KAAI,SAAC+E,EAAK6C,GACd,IAAMzH,EAAK4E,EAAK5E,GAChB,GAAKA,GAKL,GAAI4E,EAAKuB,OAAQ,CACf,IAAMzC,EAAM6D,EAAWG,cAAX,4BAA8C9C,EAAK5E,GAAnD,OAEZ,IAAK0D,EACH,OAGF,IAAIuE,EAAU9B,GAAOvB,EAAM,OAAQxB,GAEnCM,EAAIkE,OAAOK,EAAQJ,WAbnBf,QAAQC,KAAR,gBAAsBU,EAAtB,qBC1JAS,GAAU,SAAC,GAAyD,QAAvDnD,aAAuD,MAA/C,GAA+C,MAA3CiD,aAA2C,MAAnC,GAAmC,MAA/BR,cAA+B,MAAtB,GAAsB,MAAjB1C,cAAiB,MAAR,GAAQ,EACpEqD,EAAWlF,EAIf,OAHI6B,EAAOO,cAAgBC,kBACzB6C,EAAW5E,GAEN,CACLwB,MAAOA,EAAMlF,KAAI,SAAC2F,GAChB,wCACKA,GADL,IAEE4C,MAAOD,OAGXH,MAAOA,EAAMnI,KAAI,SAAAsC,GACf,sCACE+C,KAAM,YACH/C,GAFL,IAGEiG,MAAOtE,OAGX0D,OAAQA,EAAO3H,KAAI,SAAAoE,GACjB,wCACKA,GADL,IAEEmE,MAAOlE,SAaTmE,GAAe,SAACvD,EAAQC,EAAOuD,EAAUlF,GAGzC0B,EAAOO,cAAgBC,kBAEtBR,EAAOG,QAAUH,EAAOyD,qBACzBxD,EAAQD,EAAO0D,iBAAiBzD,GAAS,GAAIa,EAAEnH,IAAI,GAAI,cAAc,KAEvEuG,EAASF,EAAQC,IARqC,MAW5BT,EAAKS,EAAOuD,GAAjC7D,EAXiD,EAWjDA,QAASC,EAXwC,EAWxCA,QAEhBI,EAAO2D,YAAY/D,EAAQ7E,KAAI,SAAAK,GAAC,OAAIA,EAAEF,OAAK,GAE3C8E,EAAO4D,SAASR,GAAQ,CAACnD,MAAON,IAAUM,OAE1C+C,GAAYhD,EAAO6D,KAAM7D,EAAO8D,aAAa7D,MAAO,CAACA,MAAON,GAASM,MAAO3B,IAIxEyF,GAAc,SAAC/D,EAAQkD,EAAOc,EAAU1F,GAAW,MAC1BkB,EAAK0D,EAAOc,GAAjCrE,EAD+C,EAC/CA,QAASC,EADsC,EACtCA,QAEjBI,EAAOiE,YAAYrE,EAAQ7E,KAAI,SAAAK,GAAC,OAAIA,EAAEF,OAAK,GAE3C8E,EAAOkE,SAASd,GAAQ,CAACF,MAAOvD,IAAUuD,OAAO,GAEjDD,GAAYjD,EAAO6D,KAAM,CAACX,MAAOvD,GAASuD,MAAO5E,IAG7C6F,GAAgB,SAACnE,EAAQ0C,EAAQ0B,EAAW9F,GAAW,MAChCkB,EAAKkD,EAAQ0B,GAAjCzE,EADoD,EACpDA,QAASC,EAD2C,EAC3CA,QAEhBwD,GAAQ,CAACV,OAAQ9C,IAAU8C,OAAOxE,SAAQ,SAAAiB,GACxCa,EAAOqE,YAAYlF,EAAMjE,OAG3BkI,GAAQ,CAACV,OAAQ/C,IAAU+C,OAAOxE,SAAQ,SAAAiB,GACxCa,EAAOsE,SAASnF,MAGlBqD,GAAaxC,EAAO6D,KAAM,CAACnB,OAAQ/C,GAAS+C,OAAQpE,ICrFvC,YAAC0B,GACTA,IAILA,EAAOY,OAAS,WACdlF,KAAKuE,MAAM/B,SAAQ,SAAAwC,GACjBA,EAAK6D,UAAUrG,SAAQ,SAAAxD,GAChBoG,IAAE0D,WAAW9J,EAAE+J,YAGpB/J,EAAE+J,kBAIN/I,KAAKwH,MAAMhF,SAAQ,SAAA9C,GACZ0F,IAAE0D,WAAWpJ,EAAEsJ,SAGpBtJ,EAAEsJ,YAGJhJ,KAAKgH,OAAOxE,SAAQ,SAAA9C,GACb0F,IAAE0D,WAAWpJ,EAAEsJ,SAGpBtJ,EAAEsJ,aAIN1E,EAAOY,WCbT,IACEtH,KAAM,gBACN4H,MAAO,CACLrE,UAAW,CACTuD,KAAMvB,OACN8F,QAAS,iBAEXC,WAAY,CACVxE,KAAMyE,SACNF,QAAS,EAAf,WAEIG,WAAY,CACV1E,KAAMzI,OACNgN,QAAS,WACP,OAAO7H,IAGXiI,WAAY,CACV3E,KAAMzI,OACNwJ,UAAU,IAGdjK,KAtBF,WAuBI,MAAO,CACL8I,OAAQ,KACRC,MAAOvE,KAAKqJ,WAAW9E,MACvByC,OAAQhH,KAAKqJ,WAAWrC,OACxBQ,MAAOxH,KAAKqJ,WAAW7B,QAG3B8B,MAAO,CACLtC,OAAQ,CACNuC,QADN,WAEQvJ,KAAKwJ,eACLxJ,KAAKyJ,OAGTlF,MAAO,CACLgF,QADN,WAEQvJ,KAAKwJ,eACLxJ,KAAKyJ,OAGTjC,MAAO,CACL+B,QADN,WAEQvJ,KAAKwJ,eACLxJ,KAAKyJ,OAGTJ,WAAY,CACVE,QADN,WAEQvJ,KAAKuE,MAAQ,KAArB,iBACQvE,KAAKgH,OAAShH,KAAKqJ,WAAWrC,OAC9BhH,KAAKwH,MAAQxH,KAAKqJ,WAAW7B,SAInCkC,QAzDF,WAyDA,WACI1J,KAAK2J,aAEA3J,KAAKsE,QAKVtE,KAAKwJ,eAELxJ,KAAKyJ,KAELzJ,KAAK4J,MAAM,WAAY5J,MAEvBA,KAAKsE,OAAOtD,GAAG,UAAU,SAA7B,GACM,GAAkB,iBAAdxF,EAAKkJ,KACP,EAAR,qBACA,6CACQ,EAAR,qBACA,6BACQ,EAAR,qBACA,CACQ,GAAkB,aAAdlJ,EAAKkJ,KAAqB,CAAtC,IACA,2BAEU,GAAkB,OAAdmF,EAAoB,CACtB,IAAZ,kCACc,OAAOzF,EAAK5E,KAAOqK,EAAUrK,OAEX,IAAhBsK,IACF,EAAd,sBACc,EAAd,qBAEY,EAAZ,2BAGU,GAAiB,OAAbC,GAAqBnG,MAAMC,QAAQ,EAAjD,QACY,IAAZ,iCACc,OAAOO,EAAK5E,KAAOuK,EAASvK,OAEX,IAAfwK,IACF,EAAd,qBACc,EAAd,oBAEY,EAAZ,0BAIQ,EAAR,qBA5CM1D,QAAQC,KAAK,0BAiDjB0D,QAAS,CAEPN,WAFJ,WAGM,IAAN,2BACWxB,GAGH7B,QAAQ4D,IAAI,yBACZlK,KAAKoJ,WAAWjB,KAAOA,EACvBnI,KAAKsE,OAAS,IAAItE,KAAKkJ,WAAWlJ,KAAKoJ,aAJvC9C,QAAQC,KAAK,2BAuBjBiD,aA5BJ,WA6BM,GAAKxJ,KAAKsE,OAAV,CAKA,IAAN,oBACA,oBACA,qBAEM,GAAN,gCACM,GAAN,+BACM,GAAN,oCAVQgC,QAAQC,KAAK,0BAejBkD,GA7CJ,WA8CWzJ,KAAKsE,QAKV,GAAN,aACM,EAAN,cALQgC,QAAQC,KAAK,0BASjByC,OAxDJ,WAyDM,IAAN,oBACA,oBACA,qBAEM,GAAN,uBACM,GAAN,uBACM,GAAN,uBAEM,GAAN,gCACM,GAAN,+BACM,GAAN,+BACMhJ,KAAKyJ,MAGPU,aAvEJ,SAuEA,GACM,IAAN,aAEM,GAAIC,EAAM,CACR,IAAR,GACU5K,GAAI,GAAd,oHACU6K,iBAAkBD,EAAKE,eAAe9K,GACtC+K,aAAcH,EAAKI,WAAWhL,GAC9BiL,iBAAkBL,EAAKM,eAAelL,GACtCmL,aAAcP,EAAKQ,WAAWpL,IAEhCQ,KAAKwH,MAAMlL,KAAK,CACdkD,GAAI,GAAd,gHACUgL,WAAYK,EAASN,aACrBK,WAAYC,EAASF,aACrBG,OAAQD,EAASR,iBACjBU,OAAQF,EAASJ,mBAEnBzK,KAAK4J,MAAM,eAAgBiB,KAI/BG,aA7FJ,SA6FA,GACM,IAAN,aAEM,GAAIZ,EAAM,CACR,IAAR,GACU5K,GAAI4K,EAAK5K,GACT6K,iBAAkBD,EAAKE,eAAe9K,GACtC+K,aAAcH,EAAKI,WAAWhL,GAC9BiL,iBAAkBL,EAAKM,eAAelL,GACtCmL,aAAcP,EAAKQ,WAAWpL,IAExC,oCACU,OAAO4E,EAAK5E,KAAO4K,EAAK5K,MAE1BQ,KAAKwH,MAAMtK,OAAO+J,EAA1B,GACQjH,KAAK4J,MAAM,eAAgBiB,KAI/BI,cAhHJ,SAgHA,GACM,IAAN,gBACA,gBAEM,GAAIC,GAAeC,EAAa,CAC9B,IAAR,GACUC,QAAS,CACP5L,GAAI,GAAhB,oHACY6K,iBAAkBa,EAAYZ,eAAe9K,GAC7C+K,aAAcW,EAAYV,WAAWhL,GACrCiL,iBAAkBS,EAAYR,eAAelL,GAC7CmL,aAAcO,EAAYN,WAAWpL,IAEvC6L,SAAU,CACR7L,GAAI,GAAhB,oHACY6K,iBAAkBc,EAAYb,eAAe9K,GAC7C+K,aAAcY,EAAYX,WAAWhL,GACrCiL,iBAAkBU,EAAYT,eAAelL,GAC7CmL,aAAcQ,EAAYP,WAAWpL,IAEvC8L,KAAM9P,EAAK8P,MAGrB,oCACU,OAAOlH,EAAK5E,KAAOqL,EAASQ,SAAS7L,MAEvCQ,KAAKwH,MAAMtK,OAAO+J,EAA1B,GAEQjH,KAAKwH,MAAMlL,KAAK,CACdkD,GAAI,GAAd,oHACUgL,WAAYK,EAASO,QAAQb,aAC7BK,WAAYC,EAASO,QAAQT,aAC7BG,OAAQD,EAASO,QAAQf,iBACzBU,OAAQF,EAASO,QAAQX,mBAG3BzK,KAAK4J,MAAM,gBAAiBiB,KAIhCU,aAxJJ,SAwJA,GACMvL,KAAK4J,MAAM,eAAgBpO,MC1RuS,MCSpU,I,oBAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCpBX,GAAS,WAAa,IAAIuE,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAMJ,EAAIyL,GAAG,CAACtK,MAAMnB,EAAIoB,UAAUd,MAAM,CAAC,GAAK,eAAeN,EAAIP,KAAKO,EAAI0L,YAAY,CAAC1L,EAAI2L,GAAG,UAAU,CAACvL,EAAG,MAAM,CAACJ,EAAIsF,GAAG,IAAItF,EAAIuF,GAAGvF,EAAIP,IAAI,UAAU,IACtP,GAAkB,GCYtB,IACE5B,KAAM,yBACN4H,MAAO,CACLhG,GAAI,CACFkF,KAAMvB,OACNsC,UAAU,GAEZtE,UAAW,CACTuD,KAAMvB,OACN8F,QAAS,2BAEX0C,MAAO,CACLjH,KAAMzI,SAGVgO,QAAS,CACP2B,WADJ,SACA,GACM,GAAIC,EAAKC,QAAS,CAChB,IAAR,0CACQ,IAAIpH,EAKF,OAAO1E,KAAK4L,WAAWC,EAAKC,SAJ5B,GAAI,CAAC,QAAQzH,SAASK,IAAS,EAAzC,oDACY,OAAOmH,EAAKC,aAMhBxF,QAAQC,KAAK,uBAInBmD,QA/BF,WAgCI,IAAJ,wBACA,gDACQlD,IAAeA,EAAWuF,YAAY,eAAiB/L,KAAKR,KAC9DgH,EAAWwF,YAAY,OAA7B,OAA6B,CAA7B,CACQxM,GAAI,eAAiBQ,KAAKR,GAC1B0D,IAAKlD,KAAKqH,KAClB,cAIE4E,cA1CF,WA2CI,IAAJ,wBACQC,EAAgB1F,WAAWuF,YAAY,eAAiB/L,KAAKR,KAC/D0M,EAAgB1F,WAAW2F,eAAe,eAAiBnM,KAAKR,MC1DgR,MCQlV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OCfE,IDmBW,G,QCnBF,WAAa,IAAIO,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,QAAQ,CAACT,EAAG,MAAM,CAACS,YAAY,SAASC,MAAOd,EAAIwF,SAAS6G,SAASC,YAAc,6CAA8C,8BAA+B,CAACtM,EAAIsF,GAAG,IAAItF,EAAIuF,GAAGvF,EAAIwF,SAAS6G,SAASE,OAAO,OAAOnM,EAAG,MAAM,CAACS,YAAY,WAAW,CAACb,EAAIsF,GAAG,IAAItF,EAAIuF,GAAGvF,EAAIwF,SAAS6G,SAASG,SAAS,OAAQxM,EAAIwF,SAAS6G,SAAiB,SAAEjM,EAAG,MAAM,CAACS,YAAY,iBAAiBb,EAAIyM,SAC/e,GAAkB,GCatB,IACE5O,KAAM,qBACN4H,MAAO,CACLD,SAAU,CACRb,KAAMzI,QAERuK,WAAY,CACV9B,KAAMzI,SAGVwQ,WAAY,GAEZxC,QAAS,IC1BiV,MCQxV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIlK,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,cAAc,CAACT,EAAG,MAAM,CAACS,YAAY,oBAAoBC,MAAOd,EAAIwF,SAAS6G,SAASC,YAAc,6CAA8C,8BAA+B,CAACtM,EAAIsF,GAAG,IAAItF,EAAIuF,GAAGvF,EAAIwF,SAAS6G,SAASE,OAAO,OAAOnM,EAAG,MAAM,CAACS,YAAY,iBAAiBb,EAAI2M,GAAI3M,EAAc,YAAE,SAASqE,EAAK6C,GAAO,OAAO9G,EAAG,MAAM,CAACxB,IAAIsI,EAAMrG,YAAY,qBAAqBC,MAAOuD,EAAKuI,SAAW,GAAK,gBAAiB,CAAEvI,EAAa,SAAE,CAACjE,EAAG,aAAa,CAACE,MAAM,CAAC,UAAY,YAAY,MAAQ,GAAG,QAAU,UAAU,CAACF,EAAG,UAAU,CAACyM,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,gBAAgB,MAAM,SAAW,SAAS,UAAU,cAAcvM,MAAM,CAAC,SAAW+D,EAAKyI,SAAS,OAASzI,EAAKkI,SAASnM,EAAG,MAAM,CAACe,MAAMnB,EAAIwF,SAASuH,aAAe,sBAAwB,aAAazM,MAAM,CAAC,KAAO,aAAa0M,KAAK,aAAa,CAAC5M,EAAG,MAAM,CAACS,YAAY,4BAA4BP,MAAM,CAAC,IAAM,UAAS,KAAe+D,EAAW,cAAU,IAAI,CAACjE,EAAG,MAAM,CAACA,EAAG,MAAM,CAACS,YAAY,4BAA4BP,MAAM,CAAC,IAAM,UAAS,KAAe+D,EAAW,cAAUjE,EAAG,MAAM,CAACS,YAAY,2BAA2B,CAACb,EAAIsF,GAAGtF,EAAIuF,GAAGlB,EAAKkI,WAAW,MAAK,GAAIvM,EAAIwF,SAAS6G,SAASO,UAAY5M,EAAIwF,SAASuH,aAAc3M,EAAG,MAAM,CAACS,YAAY,iBAAiBb,EAAIyM,QACr1C,GAAkB,GCDlB,GAAS,WAAa,IAAIzM,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACyM,YAAY,CAAC,MAAQ,OAAO,OAAS,OAAO,SAAW,WAAW,QAAU,OAAO,iBAAiB,SAAS,WAAa,uBAAuB,CAACzM,EAAG,MAAM,CAACS,YAAY,mBAAmBgM,YAAY,CAAC,SAAW,aAAa,CAACzM,EAAG,MAAM,CAACS,YAAY,kBAAkB,CAACT,EAAG,OAAO,CAACyM,YAAY,CAAC,YAAY,OAAO,MAAQ,YAAY,CAAC7M,EAAIsF,GAAGtF,EAAIuF,GAAGvF,EAAIiN,aAAajN,EAAI2M,GAAI3M,EAAS,OAAE,SAASqE,EAAK6C,GAAO,OAAO9G,EAAG,MAAM,CAACxB,IAAIsI,EAAMrG,YAAY,aAAa,CAACT,EAAG,OAAO,CAACyM,YAAY,CAAC,YAAY,OAAO,MAAQ,UAAU,gBAAgB,QAAQ,CAACzM,EAAG,OAAO,CAACyM,YAAY,CAAC,cAAc,MAAM,MAAQ,YAAY,CAAC7M,EAAIsF,GAAGtF,EAAIuF,GAAGlB,EAAK6I,WAAW9M,EAAG,MAAM,CAACS,YAAY,iBAAiBsM,SAAS,CAAC,UAAYnN,EAAIuF,GAAGlB,EAAK5I,eAAc,MACxzB,GAAkB,G,gFCoBtB,IACEoC,KAAM,UACN6O,WAAY,GACZjH,MAAO,CACLqH,SAAU,CACRnI,KAAMd,MACN6B,UAAU,EACVwD,QAAS,WACP,MAAO,KAGX+D,OAAQ,CACNtI,KAAMvB,OACNsC,UAAU,EACVwD,QAAS,KAGbzN,KAjBF,WAkBI,MAAO,CACL2R,YAAaC,KAAKC,SAASC,SAAS,IAAIC,OAAO,EAAG,GAClDC,MAAO,GACPC,oBAAgBC,EAChBC,IAAI,IAAI,GAAd,GACA,KAAQ,MAAR,EAAQ,QAAR,EAAQ,aAAR,EAAQ,SAAR,IACA,KACQ,UAAR,YACU,MAAV,2BACA,kBAAY,SAAZ,SAAY,gBAAZ,UACA,qBAKErE,MAAO,CACLuD,SAAU,CACRtD,QADN,WAEQjD,QAAQ4D,IAAI,WAAYlK,KAAK6M,UAC7B7M,KAAKwN,MAAQ,GACb,IAAK,IAAb,gCACU,IAAV,mBACeI,EAAQpS,MAAuC,IAA/BoS,EAAQpS,KAAKqS,OAAO7R,QAGzCgE,KAAKwN,MAAMlR,KAAK,CACd2Q,KAAMW,EAAQX,KACdzR,KAAMwE,KAAK2N,GAAGhI,OAAOiI,EAAQpS,UAInCsS,MAAM,EACNC,WAAW,KCvEgU,MCS7U,I,oBAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCkBf,IACEnQ,KAAM,mBACN6O,WAAY,CACVuB,QAAJ,IAEExI,MAAO,CACLD,SAAU,CACRb,KAAMzI,QAERuK,WAAY,CACV9B,KAAMzI,SAGVT,KAbF,WAcI,MAAO,CACLyS,kBAAmB,CACzB,CACQ,MAAR,YACQ,SAAR,YACQ,OAAR,iBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,eACQ,SAAR,eACQ,OAAR,iBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,WACQ,SAAR,WACQ,OAAR,gBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,cACQ,SAAR,cACQ,OAAR,mBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,eACQ,SAAR,sBACQ,OAAR,2BACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,cACQ,SAAR,cACQ,OAAR,mBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,iBACQ,SAAR,iBACQ,OAAR,sBACQ,UAAR,EACQ,KAAR,IAEA,CACQ,MAAR,cACQ,SAAR,cACQ,OAAR,iBACQ,UAAR,EACQ,KAAR,KAGMC,WAAY,KAGhB5E,MAAO,CACL/D,SAAU,CACRgE,QADN,SACA,KACQvJ,KAAKkO,WAAaC,KAAKC,MAAMD,KAAKE,UAAUrO,KAAKiO,oBACzD,uBAEUjO,KAAKkO,WAAW1L,SAAQ,SAAlC,GACY8L,EAAOlC,SAAS8B,WAAW1L,SAAQ,SAA/C,GACA,sBACgB4B,EAAKuI,UAAW,EAChBvI,EAAKyI,SAAW0B,EAAW1B,gBAKnCvG,QAAQ4D,IAAI,oBAAqBsE,EAAQ,KAAMF,IAEjDR,MAAM,EACNC,WAAW,IAGf9D,QAAS,ICvI+U,MCQtV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCjBTwE,G,+KACCvM,GACH,IAAMwM,EAAO,kEAAWxM,GAExB,OADAoE,QAAQ4D,IAAI,gBAAiBwE,GACtBA,I,gCAECC,GACRrI,QAAQ4D,IAAI,iBAAkByE,O,GAPXrL,WAWRmL,MCMf,IACE,iBAAoB,GACpB,eAAkB,GAClB,KAAQ,IAGV,IACE7Q,KAAM,aACN4H,MAAO,CAAC,QACRiH,WAAY,CACVmC,aAAJ,IAEEpT,KANF,WAOI,MAAO,CACLsF,QAAS,CACP+N,MAAO,OACPC,OAAQ,QACRC,WAAY,oBAEdC,QAAS,GACTC,SAAU,CACR,MAAR,OACQ,OAAR,QACQ,WAAR,oBAEMlO,SAAU,GACVmO,WAAN,GACMC,aAAc,GACdnF,UAAW,IAGfV,MAAO,CACL5I,KAAM,CACJ6I,QADN,SACA,KAEQvJ,KAAKc,QAAQ+N,MAAQP,EAAO5N,KAAKmO,OAAS7O,KAAKiP,SAASJ,MACxD7O,KAAKc,QAAQgO,OAASR,EAAO5N,KAAKoO,QAA1C,qBACQ9O,KAAKc,QAAQiO,WAAaT,EAAO5N,KAAKqO,YAA9C,yBACQ,IAAR,kBACQK,EAAS7K,MAAM/B,SAAQ,SAA/B,GACUwC,EAAKW,OAAS0J,GAASrK,EAAKW,QAC5BX,EAAK,gBAAkBoK,EAAStC,gBAElCsC,EAAS5H,MAAMhF,SAAQ,SAA/B,GACUb,EAAKiG,MAAQyH,GAAS1N,EAAKiG,UAE7B5H,KAAKe,SAAWqO,EAChB9I,QAAQ4D,IAAI,oBAAqBsE,EAAQ,KAAMF,IAEjDR,MAAM,EACNC,WAAW,IAGf9D,QAAS,CACPqF,QADJ,WAEMtP,KAAKuP,SAAS/H,MAAMlL,KAAK,CACvBkD,GAAI,OACJkF,KAAM,OACNoG,OAAQ,IACRC,OAAQ,QAGZ9J,aATJ,SASA,GACMjB,KAAKmP,aAAeK,EACpBxP,KAAKkP,WAAaM,EAAOlL,OACzBpF,OAAOiQ,aAAeK,EACtBlJ,QAAQ4D,IAAI,UAAWlK,KAAKkP,eCrFoS,MCQlU,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAInP,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAwB,IAAtBJ,EAAI0P,eAAsBtP,EAAG,MAAM,CAACA,EAAG,KAAK,CAACS,YAAY,cAAc,CAACb,EAAIsF,GAAG,qBAAqBlF,EAAG,MAAM,CAACS,YAAY,YAAY,CAACb,EAAIsF,GAAGtF,EAAIuF,GAAGvF,EAAI0P,0BAAwC/B,GAAlB3N,EAAI2P,WAAyB3P,EAAI2L,GAAG,UAAU,KAAK,CAAC,KAAO3L,EAAI2P,WAAWhP,KAAK,SAAWX,EAAI2P,WAAWC,WAAW5P,EAAIyM,MAAM,IACpZ,GAAkB,G,aCqBP,gBAAIzG,OAAO,CACxBnI,KAAM,0BACNpC,KAAM,iBAAO,CACXkU,gBAAahC,EACb+B,eAAgB,KAElBxF,QAAS,CAKP2F,cAAe,SAASC,GACtB,IAAMC,EAAcD,EACpB7P,KAAK0P,WAAaI,EAAYC,OAC9B/P,KAAKyP,eAAiB,KAG1B/F,QAjBwB,WAoBtB,QAAUsG,OAAOC,iBACf,QAAUC,aACVlQ,KAAK4P,eAEP,QAAUO,oBACV,QAAUC,kBAEZjM,QA3BwB,WA4BtB,QAAUiM,kBAEZC,UA9BwB,WA+BtB,QAAUL,OAAOM,oBACf,QAAUJ,aACVlQ,KAAK4P,gBAGTW,cApCwB,SAoCVC,GACZxQ,KAAKyP,eAAiBtM,OAAOqN,MC3DiX,MCQ9Y,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCGf,IACE5S,KAAM,MACN6O,WAAY,CAAd,2CCxB8T,MCQ1T,I,UAAY,eACd,GACA,EACA9L,GACA,EACA,KACA,KACA,OAIa,M,+BCdf,aAAI+E,UAAU,cAAQ9H,KAAM,eAC5B,aAAI6S,OAAOC,eAAgB,EAC3B,aAAIxU,UAAUyU,SAAW,CAAEC,KAAM,QAASC,OAAQ,KAElD,IAAI,aAAI,CACNlL,OAAQ,SAAAmL,GAAC,OAAIA,EAAEC,OACdnK,OAAO,S,4CCXVrJ,EAAOD,QAAU,IAA0B,yC,kCCA3C,yBAAoiB,EAAG,G,kCCAviB,yBAA6c,EAAG,G,mECAhdC,EAAOD,QAAU,IAA0B", "file": "js/app.c808c0b0.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./title-content-node.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./title-content-node.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/mem_robot.44e5fdbc.webp\";", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"", "var map = {\n\t\"./configuration_robot.webp\": \"dab1\",\n\t\"./cpu_robot.webp\": \"9f0b\",\n\t\"./dba_robot.webp\": \"fbcc\",\n\t\"./index_robot.webp\": \"5cfa\",\n\t\"./io_robot.webp\": \"6069\",\n\t\"./mem_robot.webp\": \"0543\",\n\t\"./net_robot.webp\": \"75b4\",\n\t\"./query_robot.webp\": \"6004\",\n\t\"./workload_robot.webp\": \"462a\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"1771\";", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./report_flow.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./report_flow.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-endpoint.vue?vue&type=style&index=0&scope=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-endpoint.vue?vue&type=style&index=0&scope=true&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./agent_group_node.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./agent_group_node.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-node.vue?vue&type=style&index=0&id=61785935&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-node.vue?vue&type=style&index=0&id=61785935&scoped=true&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/workload_robot.a4663857.webp\";", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-group.vue?vue&type=style&index=0&id=521f46d8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-group.vue?vue&type=style&index=0&id=521f46d8&scoped=true&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/index_robot.871d66b4.webp\";", "module.exports = __webpack_public_path__ + \"img/query_robot.3342da36.webp\";", "module.exports = __webpack_public_path__ + \"img/io_robot.1c748e65.webp\";", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=style&index=0&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/net_robot.871d66b4.webp\";", "module.exports = __webpack_public_path__ + \"img/cpu_robot.4c891857.webp\";", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('WithStreamlitConnection',{scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar args = ref.args;\nreturn [_c('ReportFlow',{attrs:{\"args\":args}})]}}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"wrapper\",style:((\"width: \" + (_vm.options.width) + \"; height: \" + (_vm.options.height) + \"; background: \" + (_vm.options.background) + \";\"))},[_c('butterfly-vue',{key:\"grid\",ref:\"butterflyVue\",attrs:{\"canvasData\":_vm.nodeData,\"className\":\"grid\"},on:{\"onLoaded\":_vm.finishLoaded}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:_vm.className},[_c('div',{ref:\"canvas-dag\",staticClass:\"butterfly-vue-container\"})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "const defaultOptions = {\n  disLinkable: false, // 可删除连线\n  linkable: false,    // 可连线\n  draggable: false,   // 可拖动\n  zoomable: false,    // 可放大\n  moveable: true,    // 可平移\n  theme: {\n    edge: {\n      shapeType: 'AdvancedBezier'\n    }\n  }\n};\n\nexport {\n  defaultOptions,\n};\n", "import {Node} from 'butterfly-dag';\n\nconst getNodeStyle = (left, top) => {\n  return {\n    left: left + 'px',\n    top: top + 'px',\n    position: 'absolute'\n  };\n};\n\nclass DefaultNode extends Node {\n  \n  draw = (obj) => {\n    const div = document.createElement('div');\n    const style = getNodeStyle(obj.left, obj.top);\n    Reflect.ownKeys(style).forEach(key => {\n      div.style[key] = style[key];\n    });\n\n    div.className = 'butterfly-node';\n    div.id = 'bf_node_' + obj.id;\n\n    return div;\n  }\n\n}\n\nexport default DefaultNode;", "import {TreeNode} from 'butterfly-dag';\n\nconst getNodeStyle = (left, top) => {\n  return {\n    left: left + 'px',\n    top: top + 'px',\n    position: 'absolute'\n  };\n};\n\nclass DefaultTreeNode extends TreeNode {\n  constructor(options) {\n    super(options);\n    this.children = options.children;\n    this.parent = options.parent;\n    this.collapsed = options.collapsed || false;\n    if (options.isRoot) {\n      this.isRoot = options.isRoot;\n    }\n  }\n  \n  draw = (obj) => {\n    const div = document.createElement('div');\n    const style = getNodeStyle(obj.left, obj.top);\n    Reflect.ownKeys(style).forEach(key => {\n      div.style[key] = style[key];\n    });\n\n    div.className = 'butterfly-node';\n    div.id = 'bf_node_' + obj.id;\n\n    return div;\n  }\n\n}\n\nexport default DefaultTreeNode;", "import {Edge} from 'butterfly-dag'\n\nclass DefaultEdge extends Edge {\n  drawArrow(isShow) {\n    let dom = super.drawArrow(isShow);\n    return dom;\n  }\n  drawLabel() {\n    const div = document.createElement('div');\n\n    if (!this.id) {\n      this.id = String(Number(new Date()));\n    }\n\n    div.id = `edge_label_${this.id}`;\n    div.className = 'butterflies-label';\n\n    return div;\n  }\n}\n\nexport default DefaultEdge;", "import {Group} from 'butterfly-dag';\n\nconst getGroupStyle = (left, top) => {\n  return {\n    left: left + 'px',\n    top: top + 'px',\n    position: 'absolute'\n  };\n};\n\nclass DefaultGroup extends Group {\n  draw(group) {\n    const div = document.createElement('div');\n\n    const style = getGroupStyle(group.left, group.top);\n    Object.keys(style).forEach(key => {\n      div.style[key] = style[key];\n    });\n\n    div.className = 'butterfly-group';\n\n    div.id = `bf_group_${group.id}`;\n    div.className = 'butterflies-group';\n\n    return div;\n  }\n}\n\nexport default DefaultGroup;", "const adapterArray = (value) => {\n  return Array.isArray(value) ? value : [value];\n};\n\n/**\n * 判断新旧的版本\n * @param {Array} news\n * @param {Array} olds\n * @param {String} key 对比用的key\n */\nconst diff = (news = [], olds = [], key = 'id') => {\n  news = adapterArray(news);\n  olds = adapterArray(olds);\n\n  const created = [];\n  const deleted = [];\n  const updated = [];\n\n  for (let item of news) {\n    if (!olds.map(item => item[key]).includes(item[key])) {\n      created.push(item);\n    } else {\n      updated.push(item);\n    }\n  }\n\n  for (let item of olds) {\n    if (!news.map(item => item[key]).includes(item[key])) {\n      deleted.push(item);\n    }\n  }\n\n  return {created, deleted, updated};\n};\n\nexport default diff;", "import _ from 'lodash';\nimport {TreeCanvas} from 'butterfly-dag';\n\nexport default (canvas, nodes = null) => {\n  if (!canvas) {\n    return;\n  }\n  if (!canvas.relayout) {\n    const {type, options} = canvas.layout || {};\n\n    canvas.relayout = function (nodes) {\n      if (nodes) {\n        // 自动布局\n        canvas._autoLayout({...canvas, nodes});\n        // 重置canvas.nodes的定位，用于re-layout.js的布局\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        resetCanvasNodesPosition(canvas, nodes);\n      } else {\n        if (canvas.constructor === TreeCanvas) {\n          canvas._autoLayout(canvas);\n        } else if (type && options) {\n          canvas.autoLayout(type, options || {});\n        }\n      }\n\n      this.nodes.forEach(node => {\n        node.moveTo(node.left, node.top);\n      });\n\n      if (canvas.recalc) {\n        canvas.recalc();\n      }\n    };\n  }\n  canvas.relayout(nodes);\n};\n\nconst resetCanvasNodesPosition = (canvas, nodes) => {\n  const tempNodeObj = {};\n\n  nodes.forEach(item => {\n    tempNodeObj[item.id] = item;\n  });\n\n  canvas.nodes.forEach((item) => {\n    if (_.get(tempNodeObj,`[${item.id}].left`)) {\n      item.left = tempNodeObj[item.id].left;\n    }\n    if (_.get(tempNodeObj,`[${item.id}].top`)) {\n      item.top = tempNodeObj[item.id].top;\n    }\n  })\n}\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"vue-bf-group\"},[_c('div',{staticClass:\"vue-bf-group-header\"},[_vm._v(\" group\"+_vm._s(_vm.itemData.id)+\" \")]),_c('div',{staticClass:\"vue-bf-group-content\"})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vue-bf-group\">\n    <div class=\"vue-bf-group-header\">\n      group{{itemData.id}}\n    </div>\n    <div class=\"vue-bf-group-content\"></div> \n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: \"vue-group\",\n  props: {\n    itemData: {\n      type: Object,\n      required: true,\n    },\n  },\n};\n</script>\n\n<style scoped>\n  .vue-bf-group {\n    border-radius: 5px;\n    min-width: 250px;\n    border: 1px solid #aaa;\n  }\n\n  .vue-bf-group .vue-bf-group-header {\n    height: 30px;\n    background-color: #aaa;\n    text-align: center;\n    line-height: 30px;\n  }\n\n  .vue-bf-group .vue-bf-group-content {\n    min-height: 120px;\n  }\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-group.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-group.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./vue-group.vue?vue&type=template&id=521f46d8&scoped=true&\"\nimport script from \"./vue-group.vue?vue&type=script&lang=js&\"\nexport * from \"./vue-group.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vue-group.vue?vue&type=style&index=0&id=521f46d8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"521f46d8\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"vue-bf-node\"},[_vm._v(\" node\"+_vm._s(_vm.itemData.id)+\" \")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"vue-bf-node\">\n    node{{itemData.id}}\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: \"vue-node\",\n  props: {\n    itemData: {\n      type: Object,\n      required: true,\n    },\n  },\n};\n</script>\n\n<style scoped>\n  .vue-bf-node {\n    width: 100px;\n    height: 25px;\n    border-radius: 5px;\n    background-color: white;\n    border: 1px solid #aaa;\n    text-align: center;\n  }\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-node.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-node.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./vue-node.vue?vue&type=template&id=61785935&scoped=true&\"\nimport script from \"./vue-node.vue?vue&type=script&lang=js&\"\nexport * from \"./vue-node.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vue-node.vue?vue&type=style&index=0&id=61785935&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61785935\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue';\nimport _ from 'lodash';\n\nimport VueGroup from '../coms/vue-group.vue';\nimport VueNode from '../coms/vue-node.vue';\n\n/**\n * 渲染render\n * @param {Object} item 渲染对象（就是mockdata中的每一项）\n * @param {String} type 渲染类型\n * @param {Array} canvasNodes 渲染节点对应的渲染类型\n */\nconst render = (item, type, parent = null, canvasNodes = null) => {\n\n  let vueCon;\n\n  if (item.render) {\n    switch (typeof item.render) {\n      case 'string':\n        vueCon = Vue.extend({\n          template: item.render,\n          props:{\n            itemData: {\n              type: Object,\n              required: true,\n            }\n          },\n        });\n        break;\n\n      case 'object':\n        vueCon = Vue.extend(item.render);\n        break;\n\n      default:\n        throw Error(`${type}存在render属性，应该为string或者object类型，现在是${typeof render}`);\n    }\n  } else {\n    switch (type) {\n      case 'group':\n        vueCon = Vue.extend(VueGroup);\n        break;\n\n      case 'node':\n        vueCon = Vue.extend(VueNode);\n        break;\n\n      default:\n        break;\n    }\n    \n  }\n\n  let propsData = {\n    itemData: item,\n  }\n\n  if (type === 'node') {\n    let canvasNodeIndex = canvasNodes.findIndex((node)=>{\n      return node.id === item.id;\n    })\n  \n    if (canvasNodeIndex === -1) {\n      console.warn(`canvas.addNodes方法出错`);\n      return null;\n    }\n  \n    let canvasNode = canvasNodes[canvasNodeIndex];\n\n    propsData.canvasNode = canvasNode;\n  \n    const nodeCon = new vueCon({\n      parent: parent,\n      propsData\n    });\n    nodeCon.$butterfly = {\n      type: type\n    }\n    // 暂时不用指向parent 节点的emit支持\n    // nodeCon.$parent = parent\n    // 打通组件的$emit事件传输\n    nodeCon._events = parent._events;\n    nodeCon.$mount();\n    \n    return nodeCon;\n  }\n  else {\n    const Con = new vueCon({\n      propsData\n    })\n    // 暂时不用指向parent  edge和group的emit支持\n    // Con.$parent = parent\n    // 打通组件的$emit事件传输\n    Con._events = parent._events;\n    Con.$mount();\n    \n    return Con;\n  }\n\n};\n\nconst addCom = (proData) => {\n  addGroupsCom(proData.groups);\n  addNodesCom(proData.nodes);\n};\n\nconst addGroupsCom = (canvasRoot, groups, parent) => {\n  groups.map((item,index) => {\n    const id = item.id;\n    if (!id) {\n      console.warn(`groups的${index}不含ID属性，请检查格式`);\n      return;\n    }\n\n    const dom = canvasRoot.querySelector(`*[id^='bf_group_${item.id}']`);\n\n    if (!dom) {\n      return;\n    }\n\n    let groupCon = render(item, 'group', parent);\n\n    dom.append(groupCon.$el);\n\n  });\n};\n\nconst addNodesCom = (canvasRoot, canvasNodes, nodes, parent) => {\n  nodes.map((item,index) => {\n    if (_.isArray(item)) {\n      return ;\n    }\n    const id = item.id;\n    if (!id) {\n      console.warn(`nodes的${index}不含ID属性，请检查格式`);\n      return;\n    }\n\n    const dom = canvasRoot.querySelector(`*[id^='bf_node_${item.id}']`);\n\n    if (!dom) {\n      return;\n    }\n\n    let nodeCon = render(item, 'node', parent, canvasNodes);\n\n    dom.append(nodeCon.$el);\n\n    // 需要先挂载锚点才可以添加锚点\n    let canvasNodeIndex = canvasNodes.findIndex((node)=>{\n      return node.id === item.id;\n    })\n  \n    if (canvasNodeIndex === -1) {\n      console.warn(`canvas.addNodes方法出错`);\n      return null;\n    }\n  })\n};\n\nconst addEdgesCom = (canvasRoot, edges, parent) => {\n  edges.map((item,index) => {\n    const id = item.id;\n    if (!id) {\n      console.warn(`edges的${index}不含ID属性，请检查格式`);\n      return;\n    }\n\n    if (item.render) {\n      const dom = canvasRoot.querySelector(`*[id^='edge_label_${item.id}']`);\n\n      if (!dom) {\n        return;\n      }\n\n      let edgeCon = render(item, 'edge', parent);\n\n      dom.append(edgeCon.$el);\n    }\n  })\n};\n\nexport {\n  addCom,\n  addEdgesCom,\n  addGroupsCom,\n  addNodesCom\n};\n", "import {TreeCanvas} from 'butterfly-dag';\nimport Node from '../coms/node';\nimport TreeNode from '../coms/tree-node';\nimport Edge from '../coms/edge';\nimport Group from '../coms/group';\nimport diff from './diff';\nimport relayout from './re-layout';\nimport {addNodesCom , addEdgesCom , addGroupsCom} from './add-com';\n\n// treeNode还没有使用起来。待解决\nconst process = ({ nodes = [], edges = [], groups = [] , canvas = {}}) => {\n  let BaseNode = Node;\n  if (canvas.constructor === TreeCanvas) {\n    BaseNode = TreeNode;\n  }\n  return {\n    nodes: nodes.map((node) => {\n      return {\n        ...node,\n        Class: BaseNode,\n      };\n    }),\n    edges: edges.map(edge => {\n      return {\n        type: 'endpoint',\n        ...edge,\n        Class: Edge,\n      };\n    }),\n    groups: groups.map(group => {\n      return {\n        ...group,\n        Class: Group,\n      };\n    })\n  };\n};\n\n/**\n * \n * @param {Canvas} canvas \n * @param {Array} nodes 新节点\n * @param {Array} oldNodes 老节点\n * @param {Object} parent butterfly-vue\n */\nconst processNodes = (canvas, nodes, oldNodes, parent) => {\n  \n  // 判断是TreeCanvas\n  if (canvas.constructor === TreeCanvas) {\n    // 对nodes进行拆解\n    if(canvas.layout && canvas.previousIsFlatNode) {\n      nodes = canvas._handleTreeNodes(nodes || [], _.get({}, 'isFlatNode', false))\n    }\n    relayout(canvas, nodes);\n  }\n\n const { created, deleted } = diff(nodes, oldNodes);\n\n  canvas.removeNodes(deleted.map(e => e.id), true);\n\n  canvas.addNodes(process({nodes: created}).nodes);\n  \n  addNodesCom(canvas.root, canvas.getDataMap().nodes, {nodes: created}.nodes, parent);\n\n};\n\nconst processEdge = (canvas, edges, oldEdges, parent) => {\n  const { created, deleted } = diff(edges, oldEdges);\n\n  canvas.removeEdges(deleted.map(e => e.id), true);\n\n  canvas.addEdges(process({edges: created}).edges, true);\n  \n  addEdgesCom(canvas.root, {edges: created}.edges, parent);\n};\n\nconst processGroups = (canvas, groups, oldGroups, parent) => {\n  const {created, deleted} = diff(groups, oldGroups);\n\n  process({groups: deleted}).groups.forEach(group => {\n    canvas.removeGroup(group.id);\n  });\n\n  process({groups: created}).groups.forEach(group => {\n    canvas.addGroup(group);\n  });\n\n  addGroupsCom(canvas.root, {groups: created}.groups, parent);\n};\n\nexport {\n  process,\n  processNodes,\n  processEdge,\n  processGroups\n};\n", "import _ from 'lodash';\n\nexport default (canvas) => {\n  if (!canvas) {\n    return;\n  }\n\n  canvas.recalc = function () {\n    this.nodes.forEach(node => {\n      node.endpoints.forEach(p => {\n        if (!_.isFunction(p.updatePos)) {\n          return;\n        }\n        p.updatePos();\n      });\n    });\n\n    this.edges.forEach(e => {\n      if (!_.isFunction(e.redraw)) {\n        return;\n      }\n      e.redraw();\n    });\n\n    this.groups.forEach(e => {\n      if (!_.isFunction(e.redraw)) {\n        return;\n      }\n      e.redraw();\n    });\n  };\n\n  canvas.recalc();\n};\n", "<template>\n  <div :class=\"className\">\n    <div class=\"butterfly-vue-container\" ref=\"canvas-dag\"></div>\n  </div>\n</template>\n\n<script>\nimport 'butterfly-dag/dist/index.css';\nimport { Canvas } from 'butterfly-dag';\nimport { defaultOptions } from './util/default-data.js';\nimport {\n  processNodes,\n  processEdge,\n  processGroups,\n} from './util/process.js';\n\nimport recalc from './util/re-calc.js';\nimport relayout from './util/re-layout.js';\n\nexport default {\n  name: \"butterfly-vue\",\n  props: {\n    className: {\n      type: String,\n      default: 'butterfly-vue',\n    },\n    baseCanvas: {\n      type: Function,\n      default: Canvas,\n    },\n    canvasConf: {\n      type: Object,\n      default: () => {\n        return defaultOptions;\n      },\n    },\n    canvasData: {\n      type: Object,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      canvas: null,\n      nodes: this.canvasData.nodes,\n      groups: this.canvasData.groups,\n      edges: this.canvasData.edges,\n    };\n  },\n  watch: {\n    groups: {\n      handler() {\n        this.updateCavans();\n        this.re();\n      }\n    },\n    nodes: {\n      handler() {\n        this.updateCavans();\n        this.re();\n      }\n    },\n    edges: {\n      handler() {\n        this.updateCavans();\n        this.re();\n      }\n    },\n    canvasData: {\n      handler() {\n        this.nodes =  this.canvasData.nodes;\n        this.groups = this.canvasData.groups;\n        this.edges = this.canvasData.edges;\n      }\n    }\n  },\n  mounted() {\n    this.initCanvas();\n\n    if (!this.canvas) {\n      console.warn(\"当前canvas为null，初始化存在问题\");\n      return;\n    }\n\n    this.updateCavans();\n\n    this.re();\n\n    this.$emit(\"onLoaded\", this);\n\n    this.canvas.on(\"events\", (data) => {\n      if (data.type === \"link:connect\") {\n        this.onCreateEdge(data);\n      } else if (data.type === \"links:delete\" && data.links.length > 0) {\n        this.onDeleteEdge(data);\n      } else if (data.type === \"link:reconnect\") {\n        this.onChangeEdges(data);\n      } else {\n        if (data.type === 'drag:end') {\n          const {dragGroup, dragNode} = data;\n\n          if (dragGroup !== null) {\n            const groupIndex = this.groups.findIndex((item) => {\n              return item.id === dragGroup.id;\n            })\n            if (groupIndex !== -1) {\n              this.groups[groupIndex].left = dragGroup.left;\n              this.groups[groupIndex].top = dragGroup.top;\n            }\n            this.canvasData.groups = this.groups;\n          }\n\n          if (dragNode !== null && Array.isArray(this.nodes)) {\n            const nodeIndex = this.nodes.findIndex((item) => {\n              return item.id === dragNode.id;\n            })\n            if (nodeIndex !== -1) {\n              this.nodes[nodeIndex].left = dragNode.left;\n              this.nodes[nodeIndex].top = dragNode.top;\n            }\n            this.canvasData.nodes = this.nodes;\n          }\n\n        }\n        this.onOtherEvent(data);\n      }\n    });\n    // window.canvas = this.canvas;\n  },\n  methods: {\n    // 初始化\n    initCanvas() {\n      const root = this.$refs[\"canvas-dag\"];\n      if (!root) {\n        console.warn(\"当前canvas没有绑定dom节点，无法渲染\");\n      } else {\n        console.log('initCanvas***********');\n        this.canvasConf.root = root;\n        this.canvas = new this.baseCanvas(this.canvasConf);\n\n        // setTimeout(() => {\n        //   this.canvas.setGridMode(true, {\n        //     isAdsorb: false,         // 是否自动吸附,默认关闭\n        //     theme: {\n        //       shapeType: 'circle',     // 展示的类型，支持line & circle\n        //       gap: 20,               // 网格间隙\n        //       background: 'rgba(0, 0, 0, 1)',     // 网格背景颜色\n        //       circleRadiu: 1.5,        // 圆点半径\n        //       circleColor: 'rgba(255, 255, 255, 1)'\n        //     }\n        //   })\n        // }, 300);\n\n      }\n    },\n\n    // 更新画布信息\n    updateCavans() {\n      if (!this.canvas) {\n        console.warn(\"当前canvas为null，初始化存在问题\");\n        return;\n      }\n\n      const oldNodes = this.canvas.nodes;\n      const oldEdges = this.canvas.edges;\n      const oldGroups = this.canvas.groups;\n\n      processGroups(this.canvas, this.groups, oldGroups, this);\n      processNodes(this.canvas, this.nodes, oldNodes, this);\n      processEdge(this.canvas, this.edges, oldEdges, this);\n\n    },\n\n    // 重新计算节点和边的位置\n    re() {\n      if (!this.canvas) {\n        console.warn(\"当前canvas为null，初始化存在问题\");\n        return;\n      }\n\n      recalc(this.canvas);\n      relayout(this.canvas);\n    },\n\n    // 重绘所有节点\n    redraw() {\n      const oldNodes = this.canvas.nodes;\n      const oldEdges = this.canvas.edges;\n      const oldGroups = this.canvas.groups;\n\n      processEdge(this.canvas, [], oldEdges, this);\n      processNodes(this.canvas ,[] , oldNodes, this);\n      processGroups(this.canvas, [], oldGroups, this);\n\n      processGroups(this.canvas, this.groups, oldGroups, this);\n      processNodes(this.canvas, this.nodes, oldNodes, this);\n      processEdge(this.canvas, this.edges, oldEdges, this);\n      this.re();\n    },\n\n    onCreateEdge(data) {\n      const link = data.links[0];\n\n      if (link) {\n        const edgeInfo = {\n          id: `${link.sourceNode.id}.${link.sourceEndpoint.id}-${link.targetNode.id}.${link.targetEndpoint.id}`,\n          sourceEndpointId: link.sourceEndpoint.id,\n          sourceNodeId: link.sourceNode.id,\n          targetEndpointId: link.targetEndpoint.id,\n          targetNodeId: link.targetNode.id,\n        };\n        this.edges.push({\n          id: `${edgeInfo.sourceNodeId}.${edgeInfo.sourceEndpointId}-${edgeInfo.targetNodeId}.${edgeInfo.targetEndpointId}`,\n          sourceNode: edgeInfo.sourceNodeId,\n          targetNode: edgeInfo.targetNodeId,\n          source: edgeInfo.sourceEndpointId,\n          target: edgeInfo.targetEndpointId,\n        });\n        this.$emit(\"onCreateEdge\", edgeInfo);\n      }\n    },\n\n    onDeleteEdge(data) {\n      const link = data.links[0];\n\n      if (link) {\n        const edgeInfo = {\n          id: link.id,\n          sourceEndpointId: link.sourceEndpoint.id,\n          sourceNodeId: link.sourceNode.id,\n          targetEndpointId: link.targetEndpoint.id,\n          targetNodeId: link.targetNode.id,\n        };\n        const index = this.edges.findIndex((item) => {\n          return item.id === link.id;\n        });\n        this.edges.splice(index,1);\n        this.$emit(\"onDeleteEdge\", edgeInfo);\n      }\n    },\n\n    onChangeEdges(data) {\n      const addLinkData = data.addLinks[0];\n      const delLinkData = data.delLinks[0];\n\n      if (addLinkData && delLinkData) {\n        const edgeInfo = {\n          addLink: {\n            id: `${addLinkData.sourceNode.id}.${addLinkData.sourceEndpoint.id}-${addLinkData.targetNode.id}.${addLinkData.targetEndpoint.id}`,\n            sourceEndpointId: addLinkData.sourceEndpoint.id,\n            sourceNodeId: addLinkData.sourceNode.id,\n            targetEndpointId: addLinkData.targetEndpoint.id,\n            targetNodeId: addLinkData.targetNode.id,\n          },\n          delLinks: {\n            id: `${delLinkData.sourceNode.id}.${delLinkData.sourceEndpoint.id}-${delLinkData.targetNode.id}.${delLinkData.targetEndpoint.id}`,\n            sourceEndpointId: delLinkData.sourceEndpoint.id,\n            sourceNodeId: delLinkData.sourceNode.id,\n            targetEndpointId: delLinkData.targetEndpoint.id,\n            targetNodeId: delLinkData.targetNode.id,\n          },\n          info: data.info,\n        };\n\n        const index = this.edges.findIndex((item) => {\n          return item.id === edgeInfo.delLinks.id;\n        });\n        this.edges.splice(index,1);\n\n        this.edges.push({\n          id: `${addLinkData.sourceNode.id}.${addLinkData.sourceEndpoint.id}-${addLinkData.targetNode.id}.${addLinkData.targetEndpoint.id}`,\n          sourceNode: edgeInfo.addLink.sourceNodeId,\n          targetNode: edgeInfo.addLink.targetNodeId,\n          source: edgeInfo.addLink.sourceEndpointId,\n          target: edgeInfo.addLink.targetEndpointId,\n        });\n\n        this.$emit(\"onChangeEdges\", edgeInfo);\n      }\n    },\n\n    onOtherEvent(data) {\n      this.$emit(\"onOtherEvent\", data);\n    },\n  }\n};\n</script>\n\n<style>\n.butterflies-link {\n  fill: none;\n  stroke: #4B4F6B;\n  stroke-width: 3px;\n}\n</style>\n\n<style scope>\n.butterfly-vue {\n  min-height: 500px;\n  min-width: 500px;\n  width: 100%;\n  height: 100%;\n  display: block;\n  position: relative;\n}\n\n.butterfly-vue-container {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  display: block;\n}\n\n.butterfly-node {\n  position: absolute;\n  user-select: none;\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./butterfly-vue.vue?vue&type=template&id=12b3d06c&\"\nimport script from \"./butterfly-vue.vue?vue&type=script&lang=js&\"\nexport * from \"./butterfly-vue.vue?vue&type=script&lang=js&\"\nimport style0 from \"./butterfly-vue.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./butterfly-vue.vue?vue&type=style&index=1&scope=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',_vm._g({class:_vm.className,attrs:{\"id\":'bf_endpoint_'+_vm.id}},_vm.$listeners),[_vm._t(\"default\",[_c('div',[_vm._v(\" \"+_vm._s(_vm.id)+\" \")])])],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div v-on=\"$listeners\" :id=\"'bf_endpoint_'+id\" :class=\"className\">\n    <slot>\n      <div>\n        {{id}}\n      </div>\n    </slot>\n  </div>\n</template>\n\n<script>\nimport _ from 'lodash';\n\nexport default {\n  name: \"butterfly-vue-endpoint\",\n  props: {\n    id: {\n      type: String,\n      required: true,\n    },\n    className: {\n      type: String,\n      default: 'vue-bf-endpoint-default'\n    },\n    param: {\n      type: Object\n    }\n  },\n  methods: {\n    findParent(self) {\n      if (self.$parent) {\n        let type = _.get(self, '$parent.$butterfly.type', false);\n        if (type) {\n          if (['node'].includes(type) && _.get(self, '$parent.$options.propsData.canvasNode', false)) {\n            return self.$parent;\n          }\n        } else {\n          return this.findParent(self.$parent)\n        }\n      } else {\n        console.warn('锚点没有被node包裹上,请检查！');\n      }\n    }\n  },\n  mounted() {\n    let butterflyParent = this.findParent(this);\n    let canvasNode = _.get(butterflyParent, '$options.propsData.canvasNode', false);\n    if (canvasNode && !canvasNode.getEndpoint('bf_endpoint_' + this.id)) {\n      canvasNode.addEndpoint({\n        id: 'bf_endpoint_' + this.id,\n        dom: this.$el,\n        ...this.param\n      });\n    }\n  },\n  beforeDestroy() {\n    let butterflyParent = this.findParent(this);\n    if (butterflyParent.canvasNode.getEndpoint('bf_endpoint_' + this.id)) {\n      butterflyParent.canvasNode.removeEndpoint('bf_endpoint_' + this.id);\n    }\n  }\n};\n</script>\n\n<style scope>\n  .vue-bf-endpoint-default {\n    display: inline-block;\n    width: 22px;\n    height: 22px;\n    border-radius: 5px;\n    background-color: white;\n    border: 1px solid #aaa;\n    text-align: center;\n  }\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-endpoint.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./vue-endpoint.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./vue-endpoint.vue?vue&type=template&id=f47f1bbe&\"\nimport script from \"./vue-endpoint.vue?vue&type=script&lang=js&\"\nexport * from \"./vue-endpoint.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vue-endpoint.vue?vue&type=style&index=0&scope=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"node\"},[_c('div',{staticClass:\"header\",style:(_vm.itemData.userData.isCompleted ? 'background-color: RGBA(103, 194, 58, 0.9);': 'background-color: #3C3A3A;')},[_vm._v(\" \"+_vm._s(_vm.itemData.userData.title)+\" \")]),_c('div',{staticClass:\"content\"},[_vm._v(\" \"+_vm._s(_vm.itemData.userData.content)+\" \")]),(_vm.itemData.userData.isRuning)?_c('div',{staticClass:\"blinking-dot\"}):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"node\">\n    <div class=\"header\" :style=\"itemData.userData.isCompleted ? 'background-color: RGBA(103, 194, 58, 0.9);': 'background-color: #3C3A3A;'\">\n      {{itemData.userData.title}}\n    </div>\n    <div class=\"content\">\n      {{itemData.userData.content}}\n    </div>\n    <div v-if=\"itemData.userData.isRuning\" class=\"blinking-dot\"></div>\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: \"title-content-node\",\n  props: {\n    itemData: {\n      type: Object,\n    },\n    canvasNode: {\n      type: Object\n    }\n  },\n  components: {\n  },\n  methods: {}\n};\n</script>\n\n<style>\n\n.node {\n  width: 240px;\n  box-shadow: 0 2px 3px 0 rgba(0,112,204,0.06);;\n  border-radius:8px;\n  overflow: hidden;\n  position: relative;\n}\n\n.header {\n  position: relative;\n  padding: 5px 20px;\n  border-radius: 5px 5px 0 0;\n  color: #FFF;\n  border: none;\n  min-height: 10px;\n  background-color: #3C3A3A;\n  text-align: center;\n  font-size: 14px;\n}\n\n.content {\n  position: relative;\n  color: #ffffff;\n  padding: 10px;\n  border-top: 2px solid #000000;\n  border-radius: 0 0 5px 5px;\n  min-height: 60px;\n  font-size: 12px;\n  background-color: #3C3A3A;\n  word-break: break-all;\n  height: 60px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./title-content-node.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./title-content-node.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./title-content-node.vue?vue&type=template&id=b824cff2&\"\nimport script from \"./title-content-node.vue?vue&type=script&lang=js&\"\nexport * from \"./title-content-node.vue?vue&type=script&lang=js&\"\nimport style0 from \"./title-content-node.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"group_node\"},[_c('div',{staticClass:\"group_node_header\",style:(_vm.itemData.userData.isCompleted ? 'background-color: RGBA(103, 194, 58, 0.9);': 'background-color: #3C3A3A;')},[_vm._v(\" \"+_vm._s(_vm.itemData.userData.title)+\" \")]),_c('div',{staticClass:\"group_content\"},_vm._l((_vm.expertData),function(item,index){return _c('div',{key:index,staticClass:\"group_content_item\",style:(item.isRuning ? '' : 'opacity: 0.5')},[(item.isRuning)?[_c('el-popover',{attrs:{\"placement\":\"top-start\",\"title\":\"\",\"trigger\":\"click\"}},[_c('OneChat',{staticStyle:{\"width\":\"360px\",\"height\":\"400px\",\"border-radius\":\"8px\",\"overflow\":\"hidden\",\"z-index\":\"9999999999\"},attrs:{\"messages\":item.messages,\"sender\":item.title}}),_c('div',{class:_vm.itemData.isDiagnosing ? 'blinking-avatar-dot' : 'avatar-dot',attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('img',{staticClass:\"group_content_item_avatar\",attrs:{\"src\":require((\"@/assets/\" + (item.avatar)))}})])],1)]:[_c('div',[_c('img',{staticClass:\"group_content_item_avatar\",attrs:{\"src\":require((\"@/assets/\" + (item.avatar)))}})])],_c('div',{staticClass:\"group_content_item_text\"},[_vm._v(_vm._s(item.title))])],2)}),0),(_vm.itemData.userData.isRuning && _vm.itemData.isDiagnosing)?_c('div',{staticClass:\"blinking-dot\"}):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\",\"position\":\"relative\",\"display\":\"flex\",\"flex-direction\":\"column\",\"background\":\"rgb(242, 246, 255)\"}},[_c('div',{staticClass:\"scroll-container\",staticStyle:{\"position\":\"relative\"}},[_c('div',{staticClass:\"message-header\"},[_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"#FFFFFF\"}},[_vm._v(_vm._s(_vm.sender))])]),_vm._l((_vm.chats),function(item,index){return _c('div',{key:index,staticClass:\"text-item\"},[_c('span',{staticStyle:{\"font-size\":\"14px\",\"color\":\"#333333\",\"margin-bottom\":\"5px\"}},[_c('span',{staticStyle:{\"margin-left\":\"5px\",\"color\":\"#666666\"}},[_vm._v(_vm._s(item.time))])]),_c('div',{staticClass:\"report-content\",domProps:{\"innerHTML\":_vm._s(item.data)}})])})],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div style=\"width: 100%; height: 100%; position: relative; display: flex; flex-direction: column; background: rgb(242, 246, 255)\">\n    <div class=\"scroll-container\" style=\"position: relative\">\n      <div\n        class=\"message-header\">\n        <span style=\"font-size: 16px; color: #FFFFFF;\">{{ sender }}</span>\n      </div>\n      <div v-for=\"(item, index) in chats\" :key=\"index\" class=\"text-item\">\n            <span style=\"font-size: 14px; color: #333333; margin-bottom: 5px\">\n              <span style=\"margin-left: 5px; color: #666666\">{{item.time}}</span>\n            </span>\n        <div class=\"report-content\" v-html=\"item.data\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\n\nexport default {\n  name: 'OneChat',\n  components: {},\n  props: {\n    messages: {\n      type: Array,\n      required: true,\n      default: function() {\n        return []\n      }\n    },\n    sender: {\n      type: String,\n      required: true,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      componentId: Math.random().toString(36).substr(2, 9),\n      chats: [],\n      scrollObserver: undefined,\n      md: new MarkdownIt()\n        .set({ html: true, breaks: true, typographer: true, linkify: true })\n        .set({\n          highlight: function(code) {\n            return '<pre class=\"hljs\"><code>' +\n              hljs.highlight(code, { language: 'python', ignoreIllegals: true }).value +\n              '</code></pre>'\n          }\n        })\n    }\n  },\n  watch: {\n    messages: {\n      handler() {\n        console.log('=======:', this.messages)\n        this.chats = []\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i]\n          if (!message.data || message.data.trim().length === 0) {\n            continue\n          }\n          this.chats.push({\n            time: message.time,\n            data: this.md.render(message.data)\n          })\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  }\n}\n</script>\n\n<style>\n.text-item {\n  margin: 20px 10px;\n  align-items: flex-start;\n  justify-content: flex-start;\n  word-break: break-all;\n  word-wrap: break-word;\n  overflow-x: scroll;\n  display: flex;\n  flex-direction: column;\n}\n\n.report-content {\n  color: #333333;\n  font-size: 14px;\n  min-height: 20px;\n  border-radius: 20px;\n  padding: 6px 12px;\n  line-height: 20px;\n  background-color: #ffffff;\n  word-break: break-all;\n  word-wrap: break-word;\n  position: relative;\n}\n\n.json-viewer {\n  width: 100%;\n}\n\ndetails {\n  padding: 10px;\n  background-color: rgba(23, 95, 255, 0.1);\n  border-radius: 8px;\n  margin: 10px 0;\n}\n\n</style>\n\n<style lang=\"scss\" scoped>\n\n.json-viewer {\n  width: 100%;\n}\n\n.message-header {\n  position: sticky;\n  top: 0;\n  width: 100%;\n  z-index: 10;\n  padding: 5px 10px;\n  background-color: #7649af;\n  text-align: center;\n}\n\n.scroll-container {\n  transition: all 0.1s ease;\n  overflow-y: auto;\n  overflow-x: hidden;\n  position: relative;\n  padding-bottom: 20px;\n  width: 100%;\n  height: 100%;\n  box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.1);\n\n  .item-space {\n    height: 15px;\n  }\n\n  .time {\n    color: #666;\n    font-size: 12px;\n    text-align: center;\n    margin-bottom: 10px;\n  }\n\n  .time-item {\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 10px;\n    font-size: 15px;\n    color: #9d9d9d;\n  }\n\n  .face {\n    width: 40px;\n    height: 40px;\n    border-radius: 40px;\n    margin-right: 7px;\n  }\n}\n</style>\n\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OneChat.vue?vue&type=template&id=2b793b10&scoped=true&\"\nimport script from \"./OneChat.vue?vue&type=script&lang=js&\"\nexport * from \"./OneChat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OneChat.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./OneChat.vue?vue&type=style&index=1&id=2b793b10&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2b793b10\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"group_node\">\n    <div class=\"group_node_header\" :style=\"itemData.userData.isCompleted ? 'background-color: RGBA(103, 194, 58, 0.9);': 'background-color: #3C3A3A;'\">\n      {{itemData.userData.title}}\n    </div>\n    <div class=\"group_content\">\n      <div v-for=\"(item, index) in expertData\" :style=\"item.isRuning ? '' : 'opacity: 0.5'\" :key=\"index\"\n           class=\"group_content_item\">\n\n        <template v-if=\"item.isRuning\">\n          <el-popover\n              placement=\"top-start\"\n              title=\"\"\n              trigger=\"click\"\n             >\n            <OneChat style=\"width: 360px; height: 400px; border-radius: 8px; overflow: hidden; z-index: 9999999999\" :messages=\"item.messages\"\n                     :sender=\"item.title\"></OneChat>\n            <div slot=\"reference\" :class=\"itemData.isDiagnosing ? 'blinking-avatar-dot' : 'avatar-dot' \">\n              <img class=\"group_content_item_avatar\" :src=\"require(`@/assets/${item.avatar}`)\">\n            </div>\n          </el-popover>\n        </template>\n        <template v-else>\n          <div>\n            <img class=\"group_content_item_avatar\" :src=\"require(`@/assets/${item.avatar}`)\">\n          </div>\n        </template>\n        <div class=\"group_content_item_text\">{{item.title}}</div>\n      </div>\n    </div>\n    <div v-if=\"itemData.userData.isRuning && itemData.isDiagnosing\" class=\"blinking-dot\"></div>\n  </div>\n</template>\n\n<script>\n\nimport OneChat from \"@/chat/OneChat\";\n\nexport default {\n  name: \"agent_group_node\",\n  components: {\n    OneChat\n  },\n  props: {\n    itemData: {\n      type: Object,\n    },\n    canvasNode: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      defaultExpertData: [\n        {\n          title: 'CpuExpert',\n          subTitle: 'CpuExpert',\n          avatar: 'cpu_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'MemoryExpert',\n          subTitle: 'MemoryExpert',\n          avatar: 'mem_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'IoExpert',\n          subTitle: 'IoExpert',\n          avatar: 'io_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'IndexExpert',\n          subTitle: 'IndexExpert',\n          avatar: 'index_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'ConfigExpert',\n          subTitle: 'ConfigurationExpert',\n          avatar: 'configuration_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'QueryExpert',\n          subTitle: 'QueryExpert',\n          avatar: 'query_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'WorkloadExpert',\n          subTitle: 'WorkloadExpert',\n          avatar: 'workload_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n        {\n          title: 'WriteExpert',\n          subTitle: 'WriteExpert',\n          avatar: 'mem_robot.webp',\n          isRuning: false,\n          role: ''\n        },\n      ],\n      expertData: []\n    }\n  },\n  watch: {\n    itemData: {\n      handler(newVal, oldVal) {\n        this.expertData = JSON.parse(JSON.stringify(this.defaultExpertData));\n        if(newVal.userData.expertData) {\n          // 将expertData中的name和role提取出来，如果expertData中的name和expertData中的subTitle相同，则将role赋值给expertData中的role，且将isRuning设置为true, 否则设置为false\n          this.expertData.forEach((item) => {\n            newVal.userData.expertData.forEach((expertItem) => {\n              if(item.subTitle === expertItem.name) {\n                item.isRuning = true;\n                item.messages = expertItem.messages;\n              }\n            })\n          })\n        }\n        console.log('Args changed from', oldVal, 'to', newVal);\n      },\n      deep: true,\n      immediate: true\n    },\n  },\n  methods: {}\n};\n</script>\n\n<style>\n\n.el-popover {\n  background: transparent!important;\n  padding: 0!important;\n  border: none!important;\n}\n\n.avatar-dot {\n  position: relative;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid #67C23A;\n}\n\n.blinking-avatar-dot {\n  position: relative;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n}\n\n.blinking-avatar-dot::after {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  width: 100%;\n  height: 100%;\n  border: 3px solid #67C23A;\n  opacity: 0;\n  border-radius: 50%;\n  animation: breathing 1.5s infinite;\n}\n\n@keyframes breathing {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.2;\n  }\n}\n\n.group_node {\n  width: 400px;\n  border-radius:8px;\n  overflow: hidden;\n  position: relative;\n}\n\n.group_node_header {\n  position: relative;\n  padding: 5px 20px;\n  border-radius: 5px 5px 0 0;\n  border: none;\n  min-height: 10px;\n  color: #ffffff;\n  background-color: #3C3A3A;\n  text-align: center;\n  font-size: 12px;\n}\n\n.group_content {\n  position: relative;\n  color: #ffffff;\n  padding: 10px;\n  border-top: 2px solid #000000;\n  border-radius: 0 0 5px 5px;\n  min-height: 60px;\n  text-align: center;\n  background-color: #3C3A3A;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-around;\n  flex-wrap: wrap;\n}\n\n.group_content_item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n.group_content_item_avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%\n}\n.group_content_item_text {\n  color: #ffffff;\n  font-size: 12px;\n  text-align: center;\n  margin-top: 2px;\n}\n\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./agent_group_node.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./agent_group_node.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./agent_group_node.vue?vue&type=template&id=3fe450da&\"\nimport script from \"./agent_group_node.vue?vue&type=script&lang=js&\"\nexport * from \"./agent_group_node.vue?vue&type=script&lang=js&\"\nimport style0 from \"./agent_group_node.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import {Edge} from 'butterfly-dag';\n\nclass BaseEdge extends Edge {\n  draw(obj) {\n    const path = super.draw(obj);\n    console.log('********path:', path);\n    return path;\n  }\n  drawLabel(texts) {\n    console.log('********texts:', texts);\n  }\n}\n\nexport default BaseEdge;\n", "<template>\n  <div class=\"wrapper\" :style=\"`width: ${ options.width }; height: ${ options.height }; background: ${ options.background };`\">\n    <butterfly-vue\n        ref=\"butterflyVue\"\n        :canvasData=\"nodeData\"\n        @onLoaded=\"finishLoaded\"\n        className='grid'\n        key=\"grid\"\n    />\n  </div>\n</template>\n\n<script>\n// import { Streamlit } from \"streamlit-component-lib\";\nimport {ButterflyVue} from './index.js';\nimport titleContentNode from './node/title-content-node';\nimport agentGroupNode from './node/agent_group_node';\nimport Edge from './util/edge';\n\nconst classMap = {\n  'titleContentNode': titleContentNode,\n  'agentGroupNode': agentGroupNode,\n  'Edge': Edge\n};\n\nexport default {\n  name: \"ReportFlow\",\n  props: [\"args\"],\n  components: {\n    ButterflyVue\n  },\n  data() {\n    return {\n      options: {\n        width: '100%',\n        height: '300px',\n        background: 'rgba(0, 0, 0, 1)',\n      },\n      runData: {},\n      defaults: {\n          width: '100%',\n          height: '300px',\n          background: 'rgba(0, 0, 0, 1)',\n      },\n      nodeData: {},\n      canvansRef:{},\n      butterflyVue: {},\n      nodeIndex: 0\n    };\n  },\n  watch: {\n    args: {\n      handler(newVal, oldVal) {\n        // Do something when `args` changes\n        this.options.width = newVal.args.width || this.defaults.width;\n        this.options.height = newVal.args.height  || this.defaults.height;\n        this.options.background = newVal.args.background  || this.defaults.background;\n        const jsonData = newVal.args.nodeData\n        jsonData.nodes.forEach(node => {\n          node.render = classMap[node.render];\n          node['isDiagnosing'] = jsonData.isDiagnosing;\n        });\n        jsonData.edges.forEach(edge => {\n          edge.Class = classMap[edge.Class];\n        });\n        this.nodeData = jsonData;\n        console.log('Args changed from', oldVal, 'to', newVal);\n      },\n      deep: true,\n      immediate: true\n    },\n  },\n  methods: {\n    addEdge() {\n      this.mockData.edges.push({\n        id: '0-29',\n        type: 'node',\n        source: '0',\n        target: '29',\n      })\n    },\n    finishLoaded(VueCom) {\n      this.butterflyVue = VueCom;\n      this.canvansRef = VueCom.canvas;\n      window.butterflyVue = VueCom;\n      console.log(\"finish:\", this.canvansRef);\n    },\n  }\n};\n</script>\n\n<style>\n.wrapper {\n  border-radius: 8px;\n  overflow: hidden;\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./report_flow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./report_flow.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./report_flow.vue?vue&type=template&id=43143727&\"\nimport script from \"./report_flow.vue?vue&type=script&lang=js&\"\nexport * from \"./report_flow.vue?vue&type=script&lang=js&\"\nimport style0 from \"./report_flow.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[(_vm.componentError != '')?_c('div',[_c('h1',{staticClass:\"err__title\"},[_vm._v(\"Component Error\")]),_c('div',{staticClass:\"err__msg\"},[_vm._v(_vm._s(_vm.componentError))])]):(_vm.renderData != undefined)?_vm._t(\"default\",null,{\"args\":_vm.renderData.args,\"disabled\":_vm.renderData.disabled}):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nimport Vue from 'vue';\nimport { RenderData, Streamlit } from \"streamlit-component-lib\";\n\nexport default Vue.extend({\n  name: \"withStreamlitConnection\",\n  data: () => ({\n    renderData: (undefined as unknown) as RenderData,\n    componentError: \"\"\n  }),\n  methods: {\n    /**\n     * Streamlit is telling this component to redraw.\n     * We save the render data in component's data.\n     */\n    onRenderEvent: function(event: Event): void {\n      const renderEvent = event as CustomEvent<RenderData>;\n      this.renderData = renderEvent.detail; \n      this.componentError = \"\"\n    }\n  },\n  mounted(): void {\n    // Set up event listeners, and signal to Streamlit that we're ready.\n    // We won't render the component until we receive the first RENDER_EVENT.\n    Streamlit.events.addEventListener(\n      Streamlit.RENDER_EVENT,\n      this.onRenderEvent\n    );\n    Streamlit.setComponentReady();\n    Streamlit.setFrameHeight();\n  },\n  updated(): void {\n    Streamlit.setFrameHeight()\n  },\n  destroyed(): void {\n    Streamlit.events.removeEventListener(\n      Streamlit.RENDER_EVENT,\n      this.onRenderEvent\n    );\n  },\n  errorCaptured(err: Error): void {\n    this.componentError = String(err);\n  }\n})\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--14-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/ts-loader/index.js??ref--14-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--14-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/ts-loader/index.js??ref--14-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"", "import { render, staticRenderFns } from \"./WithStreamlitConnection.vue?vue&type=template&id=17a60eb2&scoped=true&\"\nimport script from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"\nexport * from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"\nimport style0 from \"./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17a60eb2\",\n  null\n  \n)\n\nexport default component.exports", "<!--\n  We bootstrap our Component to Streamlit with our scoped slot in the top-level App.\n  This is where scoped slot passes Streamlit ;`args` data from itself to children MyComponent.\n  You should not have to edit this, but are free to do so :)\n-->\n<template>\n  <div id=\"app\">\n    <WithStreamlitConnection v-slot=\"{ args }\">\n      <ReportFlow :args=\"args\" />\n    </WithStreamlitConnection>\n  </div>\n</template>\n\n<script>\nimport ReportFlow from \"./report_flow.vue\";\n\n// \"withStreamlitConnection\" is a scoped slot. It bootstraps the\n// connection between your component and the Streamlit app, and handles\n// passing arguments from Python -> Component.\n//\n// You don't need to edit withStreamlitConnection (but you're welcome to!).\nimport WithStreamlitConnection from \"./streamlit/WithStreamlitConnection.vue\";\nexport default {\n  name: \"App\",\n  components: { ReportFlow, WithStreamlitConnection }\n};\n</script>\n\n<style>\nbody {\n  margin: 0;\n}\n\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n}\n\n.blinking-dot {\n  width: 14px;\n  height: 14px;\n  background-color: #67C23A;\n  border-radius: 50%;\n  position: absolute;\n  top: 7px;\n  right:7px;\n  animation: breathe 1.5s infinite;\n}\n\n@keyframes breathe {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(0.8);\n  }\n}\n.butterflie-circle-endpoint {\n  background: #67C23A !important;\n  border: 1px solid #67C23A !important;\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=b3e8bb24&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport App from './App.vue'\nimport { Popover } from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css';\n\nVue.component(Popover.name, Popover);\nVue.config.productionTip = false\nVue.prototype.$ELEMENT = { size: 'small', zIndex: 3000 };\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n", "module.exports = __webpack_public_path__ + \"img/configuration_robot.7b743a08.webp\";", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=1&id=2b793b10&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=1&id=2b793b10&lang=scss&scoped=true&\"", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=style&index=1&scope=true&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./butterfly-vue.vue?vue&type=style&index=1&scope=true&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/dba_robot.f875048d.webp\";"], "sourceRoot": ""}