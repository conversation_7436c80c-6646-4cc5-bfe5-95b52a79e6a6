//scss 语法糖包含常用的布局方式 flex column
@import './scss-suger.scss';
//重置 element-plus 样式
@import './reset-elemenet-plus-style.scss';
//动画文件
@import './transition.scss';

//reset style
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
}
* {
  box-sizing: border-box;
}
*::before,
*::after {
  box-sizing: border-box;
}
a:focus,
a:active {
  outline: none;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1;
  font-weight: 400;
  margin: 0;
  padding: 0;
}
span,
output {
  display: inline-block;
  line-height: 1;
}

//scroll
@mixin main-show-wh() {
  /* css 声明 */
  //height: calc(100vh - #{$navBarHeight} - #{$tagViewHeight} - #{$appMainPadding * 2});
  height: 100vh !important;
  width: 100%;
}
.scroll-y {
  @include main-show-wh();
  overflow-y: auto;
}
.scroll-x {
  @include main-show-wh();
  overflow-x: auto;
}
.scroll-xy {
  @include main-show-wh();
  overflow: auto;
}


.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  overflow-wrap: break-word;
}

.shadow {
  box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.04);
}
