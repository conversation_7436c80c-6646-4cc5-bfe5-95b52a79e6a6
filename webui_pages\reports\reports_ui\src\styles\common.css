.c-relative {
  position: relative;
}

.c-absolute {
  position: absolute;
}

.c-flex {
  display: flex;
}

.c-flex-wrap {
  flex-wrap: wrap;
}

.c-flex-nowrap {
  flex-wrap: nowrap;
}

.c-align-items-center {
  align-items: center;
}

.c-align-items-top {
  align-items: flex-start;
}

.c-align-items-bottom {
  align-items: flex-end;
}

.c-justify-content-center {
  justify-content: center;
}

.c-justify-content-left {
  justify-content: flex-start;
}

.c-justify-content-right {
  justify-content: flex-end;
}

.c-justify-content-between {
  justify-content: space-between;
}

.c-justify-content-around {
  justify-content: space-around;
}

.c-text-left {
  text-align: left;
}

.c-text-center {
  text-align: center;
}

.c-text-right {
  text-align: right;
}

.c-flex-column {
  display: flex;
  flex-direction: column;
}

.c-flex-row {
  display: flex;
  flex-direction: row;
}

.c-color-red {
  color: #d81e06;
}

.c-color-gray {
  color: #666666;
}

.c-color-less-gray {
  color: #999999;
}

.c-color-black {
  color: #2c2c2c;
}

.c-color-white {
  color: #FFFFFF;
}

.c-color-blue {
  color: #007AFF;
}


.c-background-red {
  background: #d81e06;
}

.c-background-black {
  background: #2c2c2c;
}

.c-shaow-card {
  background: #ffffff;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.03);
  border-radius: 20px;
}

.c-radius-card {
  background: #ffffff;
  border-radius: 32px;
}

.c-border-bottom {
  border-bottom: 1px solid #f5f5f5;
}

.container {
  width: 100%;
  height: 100%;
}

details {
  padding: 10px;
  background-color: rgba(23, 95, 255, 0.1);
  border-radius: 8px;
  margin: 10px 0;
}
