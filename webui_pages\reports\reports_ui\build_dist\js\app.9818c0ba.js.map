{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?7e02", "webpack:///./src/assets/mem_robot.webp", "webpack:///./src/streamlit/WithStreamlitConnection.vue?0067", "webpack:///./src/components/OneChat.vue?23d6", "webpack:///./src/assets/ch_to_en.png", "webpack:///./src/components/Chat.vue?f183", "webpack:///./src/assets/workload_robot.webp", "webpack:///./src/assets/index_robot.webp", "webpack:///./src/components/Chat.vue?c328", "webpack:///./src/assets/query_robot.webp", "webpack:///./src/assets/io_robot.webp", "webpack:///./src/ReportComponent.vue?fe78", "webpack:///./src/components/OneChat.vue?e9eb", "webpack:///./src/assets/cpu_robot.webp", "webpack:///./src/styles/index.scss?ac6f", "webpack:///./src/App.vue?228f", "webpack:///./src/ReportComponent.vue?f481", "webpack:///./src/components/Chat.vue?53f0", "webpack:///src/components/Chat.vue", "webpack:///./src/components/Chat.vue?61f6", "webpack:///./src/components/Chat.vue?4656", "webpack:///./src/components/OneChat.vue?b0b4", "webpack:///src/components/OneChat.vue", "webpack:///./src/components/OneChat.vue?b34e", "webpack:///./src/components/OneChat.vue?6938", "webpack:///src/ReportComponent.vue", "webpack:///./src/ReportComponent.vue?824f", "webpack:///./src/ReportComponent.vue?0da5", "webpack:///./src/streamlit/WithStreamlitConnection.vue?0397", "webpack:///./src/streamlit/WithStreamlitConnection.vue?9478", "webpack:///./src/streamlit/WithStreamlitConnection.vue?64f6", "webpack:///./src/streamlit/WithStreamlitConnection.vue?f273", "webpack:///src/App.vue", "webpack:///./src/App.vue?1160", "webpack:///./src/App.vue?bff9", "webpack:///./src/lang/zh.js", "webpack:///./src/lang/en.js", "webpack:///./src/lang/index.js", "webpack:///./src/main.ts", "webpack:///./src/assets/configuration_robot.webp", "webpack:///./src/ReportComponent.vue?0e91", "webpack:///./src/assets/dba_robot.webp"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "scopedSlots", "_u", "fn", "ref", "args", "staticRenderFns", "staticClass", "staticStyle", "$t", "on", "getAlertHistories", "model", "callback", "$$v", "expression", "_l", "item", "skipTyped", "onEnClick", "index", "alert_item", "alert_index", "style", "severityStyle", "alert_level", "_v", "_s", "alert_name", "report_generate_time", "$event", "onReviewClick", "onReportClick", "domProps", "openReport", "directives", "rawName", "reviewDrawer", "activeName", "nativeOn", "type", "indexOf", "_k", "keyCode", "undefined", "onStepClick", "stepScrollEvent", "roleAssignerMessages", "typeSpeed", "onRoleAssignerPlaybackComplete", "_e", "cpuExpertMessages", "onPlaybackComplete", "ioExpertMessages", "memoryExpertMessages", "indexExpertMessages", "configurationExpertMessages", "queryExpertMessages", "workloadExpertMessages", "brainstormingMessages", "onBrainstormingPlaybackComplete", "report", "componentId", "components", "props", "messages", "Array", "required", "default", "Boolean", "Number", "typed<PERSON>bjs", "chatText", "Math", "random", "toString", "substr", "faceMap", "scrollObserver", "phraseVisible", "md", "watch", "handler", "setTimeout", "deep", "immediate", "destroyed", "disconnect", "for<PERSON>ach", "destroy", "mounted", "MutationObserver", "target", "scrollTop", "scrollHeight", "clientHeight", "observe", "config", "methods", "dealMessage", "$emit", "message", "trim", "messagesContainer", "innerHTML", "contentContainer", "strings", "showCursor", "contentType", "onComplete", "console", "log", "e", "onPhraseItemClick", "$refs", "chatInput", "focus", "onChatConfirm", "text", "replaceAll", "$onceMessage", "info", "component", "headerStyle", "sender", "String", "chats", "filters", "timeRange", "openIndex", "expertCount", "tableMessages", "introMessage", "historyMessages", "historyLoading", "reviewItem", "reviewLoading", "analyseAt", "charts", "modelList", "computed", "oldVal", "val", "$nextTick", "$i18n", "locale", "localStorage", "setItem", "getAlertHistoryDetail", "render", "anomalyAnalysis", "RoleAssigner", "scrollToTopWithAnimation", "calcHeight", "parseInt", "setpScrollDiv", "componentError", "renderData", "_t", "disabled", "extend", "onRenderEvent", "event", "renderEvent", "detail", "events", "addEventListener", "RENDER_EVENT", "setComponentReady", "setFrameHeight", "updated", "removeEventListener", "errorCaptured", "err", "analysisButton", "timeRangeTip", "timeTip", "refreshTip", "queryTimeTip", "modelTip", "instanceTip", "reviewDrawerTitle", "timeStartTip", "timeEndTip", "playback<PERSON><PERSON><PERSON>", "reportButton", "setpTitle1", "playbackAnimationTip", "animationSpeedTip", "setpTip1", "setpTitle2", "setpTip2", "setpTitle3", "setpTip3", "reportDrawerTitle", "timeRangeSelectTip", "modelUsePrefixTip", "modelUseSuffixTip", "<PERSON><PERSON>", "use", "VueI18n", "i18n", "getItem", "zh", "en", "productionTip", "h", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,yBAAwb,EAAG,G,uBCA3bW,EAAOD,QAAU,IAA0B,+B,oCCA3C,yBAAsf,EAAG,G,6DCAzf,yBAA8c,EAAG,G,4CCAjdC,EAAOD,QAAU,8wG,oCCAjB,yBAAygB,EAAG,G,uBCA5gBC,EAAOD,QAAU,IAA0B,oC,gDCA3CC,EAAOD,QAAU,IAA0B,iC,oCCA3C,yBAAiiB,EAAG,G,qBCApiBC,EAAOD,QAAU,IAA0B,iC,qBCA3CC,EAAOD,QAAU,IAA0B,8B,kCCA3C,yBAAuhB,EAAG,G,oCCA1hB,yBAAoiB,EAAG,G,yECAviBC,EAAOD,QAAU,IAA0B,+B,4CCC3CC,EAAOD,QAAU,CAAC,SAAW,OAAO,eAAiB,UAAU,kBAAoB,UAAU,OAAS,OAAO,UAAY,UAAU,UAAY,OAAO,aAAe,Y,sKCDjK,EAAS,WAAa,IAAI+B,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,0BAA0B,CAACG,YAAYP,EAAIQ,GAAG,CAAC,CAAClB,IAAI,UAAUmB,GAAG,SAASC,GAC5M,IAAIC,EAAOD,EAAIC,KACf,MAAO,CAACP,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAOK,EAAKA,MAAQ,cAAc,IACnEC,EAAkB,GCHlB,EAAS,WAAa,IAAIZ,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,aAAaC,YAAY,CAAC,MAAQ,OAAO,YAAY,OAAO,cAAc,gBAAgB,OAAS,WAAW,CAACV,EAAG,MAAM,CAACS,YAAY,gBAAgBC,YAAY,CAAC,MAAQ,QAAQ,CAACV,EAAG,MAAM,CAACS,YAAY,yEAAyEC,YAAY,CAAC,QAAU,YAAY,gBAAgB,mBAAmB,CAACV,EAAG,UAAU,CAACM,IAAI,OAAOJ,MAAM,CAAC,QAAS,EAAK,iBAAiB,SAAS,CAACF,EAAG,eAAe,CAACU,YAAY,CAAC,gBAAgB,KAAKR,MAAM,CAAC,MAAQN,EAAIe,GAAG,YAAc,MAAM,CAACX,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,IAAIU,GAAG,CAAC,OAAShB,EAAIiB,mBAAmBC,MAAM,CAAClC,MAAOgB,EAAS,MAAEmB,SAAS,SAAUC,GAAMpB,EAAIkB,MAAME,GAAKC,WAAW,UAAUrB,EAAIsB,GAAItB,EAAa,WAAE,SAASuB,GAAM,OAAOnB,EAAG,YAAY,CAACd,IAAIiC,EAAKjB,MAAM,CAAC,MAAQiB,EAAK,MAAQA,QAAU,IAAI,IAAI,GAAGnB,EAAG,MAAM,CAACS,YAAY,kCAAkCC,YAAY,CAAC,cAAc,MAAM,CAACV,EAAG,YAAY,CAACU,YAAY,CAAC,QAAU,SAASR,MAAM,CAAC,eAAe,UAAU,iBAAiB,UAAU,cAAc,GAAG,gBAAgBN,EAAIe,GAAG,yBAAyBG,MAAM,CAAClC,MAAOgB,EAAa,UAAEmB,SAAS,SAAUC,GAAMpB,EAAIwB,UAAUJ,GAAKC,WAAW,eAAejB,EAAG,MAAM,CAACS,YAAY,aAAaC,YAAY,CAAC,cAAc,QAAQE,GAAG,CAAC,MAAQhB,EAAIyB,YAAY,CAACrB,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQR,MAAM,CAAC,IAAM,EAAQ,cAA+B,IAAI,GAAGF,EAAG,MAAM,CAACS,YAAY,uBAAuBC,YAAY,CAAC,OAAS,sBAAsB,aAAa,OAAO,OAAS,SAAS,QAAU,WAAWd,EAAIsB,GAAItB,EAAmB,iBAAE,SAASuB,EAAKG,GAAO,OAAOtB,EAAG,MAAM,CAACd,IAAIoC,EAAMb,YAAY,8BAA8BC,YAAY,CAAC,WAAa,8BAA8B,CAACV,EAAG,MAAM,CAACS,YAAY,6DAA6D,CAACT,EAAG,MAAM,CAACS,YAAY,0DAA0D,CAACb,EAAIsB,GAAIC,EAAW,QAAE,SAASI,EAAWC,GAAa,OAAOxB,EAAG,MAAM,CAACd,IAAIsC,EAAYf,YAAY,oCAAoCC,YAAY,CAAC,eAAe,SAAS,CAACV,EAAG,MAAM,CAACyB,MAAO7B,EAAI8B,cAAcH,EAAWI,cAAe,CAAC/B,EAAIgC,GAAG,KAAKhC,EAAIiC,GAAGN,EAAWI,aAAa/B,EAAIiC,GAA8B,SAA3BN,EAAWI,YAAyB,KAAO,IAAI,QAAQ3B,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAACd,EAAIgC,GAAGhC,EAAIiC,GAAGN,EAAWO,oBAAmB9B,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,UAAU,YAAY,OAAO,OAAS,OAAO,cAAc,OAAO,aAAa,QAAQ,CAACd,EAAIgC,GAAGhC,EAAIiC,GAAGV,EAAKY,0BAA0B,GAAG/B,EAAG,MAAM,CAACS,YAAY,mCAAmC,CAACT,EAAG,YAAY,CAACU,YAAY,CAAC,OAAS,UAAUR,MAAM,CAAC,KAAO,UAAU,KAAO,SAASU,GAAG,CAAC,MAAQ,SAASoB,GAAQ,OAAOpC,EAAIqC,cAAcd,MAAS,CAACvB,EAAIgC,GAAGhC,EAAIiC,GAAGjC,EAAIe,GAAG,oBAAoBX,EAAG,IAAI,CAACS,YAAY,4CAA4CC,YAAY,CAAC,YAAY,YAAYV,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,SAASU,GAAG,CAAC,MAAQ,SAASoB,GAAQ,OAAOpC,EAAIsC,cAAcf,EAAMG,MAAU,CAAC1B,EAAIgC,GAAGhC,EAAIiC,GAAGjC,EAAIe,GAAG,kBAAkBX,EAAG,IAAI,CAACS,YAAY,sCAAsCC,YAAY,CAAC,YAAY,aAAa,UAAS,KAAKV,EAAG,MAAM,CAACS,YAAY,2BAA2BC,YAAY,CAAC,aAAa,SAAS,OAAS,QAAQ,aAAa,SAAS,MAAQ,MAAM,WAAa,8BAA8B,CAACV,EAAG,MAAM,CAACU,YAAY,CAAC,mBAAmB,QAAQ,QAAU,SAAS,OAAS,SAAS,gBAAgB,OAAOyB,SAAS,CAAC,UAAYvC,EAAIiC,GAAGjC,EAAIwC,iBAAkBxC,EAAgB,aAAEI,EAAG,YAAY,CAACqC,WAAW,CAAC,CAAClE,KAAK,UAAUmE,QAAQ,YAAY1D,MAAOgB,EAAiB,cAAEqB,WAAW,kBAAkBf,MAAM,CAAC,MAAQN,EAAIe,GAAG,qBAAqB,QAAUf,EAAI2C,aAAa,KAAO,OAAO,mBAAmB,GAAG,UAAY,OAAO3B,GAAG,CAAC,iBAAiB,SAASoB,GAAQpC,EAAI2C,aAAaP,KAAU,CAAChC,EAAG,MAAM,CAACS,YAAY,2BAA2BC,YAAY,CAAC,SAAW,SAAS,OAAS,SAAS,CAACV,EAAG,WAAW,CAACU,YAAY,CAAC,MAAQ,QAAQR,MAAM,CAAC,OAASN,EAAI4C,WAAW,gBAAgB,UAAU,OAAS,KAAK,CAACxC,EAAG,UAAU,CAACU,YAAY,CAAC,OAAS,WAAWR,MAAM,CAAC,MAAQN,EAAIe,GAAG,eAAe8B,SAAS,CAAC,MAAQ,SAAST,GAAQ,OAAIA,EAAOU,KAAKC,QAAQ,QAAQ/C,EAAIgD,GAAGZ,EAAOa,QAAQ,SAAIC,EAAUd,EAAO9C,SAAI4D,GAAoB,KAAclD,EAAImD,YAAY,OAAO/C,EAAG,UAAU,CAACU,YAAY,CAAC,OAAS,WAAWR,MAAM,CAAC,MAAQN,EAAIe,GAAG,eAAe8B,SAAS,CAAC,MAAQ,SAAST,GAAQ,OAAIA,EAAOU,KAAKC,QAAQ,QAAQ/C,EAAIgD,GAAGZ,EAAOa,QAAQ,SAAIC,EAAUd,EAAO9C,SAAI4D,GAAoB,KAAclD,EAAImD,YAAY,OAAO/C,EAAG,UAAU,CAACU,YAAY,CAAC,OAAS,WAAWR,MAAM,CAAC,MAAQN,EAAIe,GAAG,eAAe8B,SAAS,CAAC,MAAQ,SAAST,GAAQ,OAAIA,EAAOU,KAAKC,QAAQ,QAAQ/C,EAAIgD,GAAGZ,EAAOa,QAAQ,SAAIC,EAAUd,EAAO9C,SAAI4D,GAAoB,KAAclD,EAAImD,YAAY,QAAQ,GAAG/C,EAAG,aAAa,CAACE,MAAM,CAAC,KAAO,SAAS,CAACF,EAAG,MAAM,CAACM,IAAI,gBAAgBG,YAAY,2BAA2BC,YAAY,CAAC,OAAS,mBAAmB,aAAa,OAAO,OAAS,UAAUE,GAAG,CAAC,OAAShB,EAAIoD,kBAAkB,CAAChD,EAAG,MAAM,CAACS,YAAY,eAAe,CAACT,EAAG,MAAM,CAACU,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,MAAQ,UAAU,cAAc,OAAO,YAAY,SAAS,CAACd,EAAIgC,GAAG,MAAMhC,EAAIiC,GAAGjC,EAAIe,GAAG,aAAa,OAAOX,EAAG,MAAM,CAACS,YAAY,kCAAkCC,YAAY,CAAC,OAAS,sBAAsB,CAAEd,EAAIqD,qBAAqB1G,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,eAAeuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,MAAQ,OAAOR,MAAM,CAAC,GAAK,eAAe,OAAS,eAAe,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAIqD,sBAAsBrC,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAIuD,+BAA+B,SAASvD,EAAIwD,KAAKpD,EAAG,MAAM,CAACS,YAAY,aAAaC,YAAY,CAAC,MAAQ,MAAM,OAAS,SAAS,CAAEd,EAAIyD,kBAAkB9G,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,YAAYuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,YAAY,OAAS,YAAY,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAIyD,mBAAmBzC,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAI2D,iBAAiBhH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,WAAWuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,WAAW,OAAS,WAAW,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAI2D,kBAAkB3C,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAI4D,qBAAqBjH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,eAAeuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,eAAe,OAAS,eAAe,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAI4D,sBAAsB5C,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAI6D,oBAAoBlH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,cAAcuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,cAAc,OAAS,cAAc,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAI6D,qBAAqB7C,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAI8D,4BAA4BnH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,sBAAsBuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,sBAAsB,OAAS,sBAAsB,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAI8D,6BAA6B9C,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAI+D,oBAAoBpH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,cAAcuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,cAAc,OAAS,cAAc,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAI+D,qBAAqB/C,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,KAAMxD,EAAIgE,uBAAuBrH,OAAS,EAAGyD,EAAG,UAAU,CAACd,IAAI,iBAAiBuB,YAAY,iBAAiBC,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,KAAO,WAAWR,MAAM,CAAC,GAAK,iBAAiB,OAAS,iBAAiB,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAIgE,wBAAwBhD,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAI0D,mBAAmB,OAAO1D,EAAIwD,MAAM,IAAI,KAAKpD,EAAG,MAAM,CAACS,YAAY,eAAe,CAACT,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,MAAQ,UAAU,cAAc,OAAO,OAAS,SAAS,YAAY,SAAS,CAACd,EAAIgC,GAAG,MAAMhC,EAAIiC,GAAGjC,EAAIe,GAAG,aAAa,OAAQf,EAAIiE,sBAAsBtH,OAAS,EAAGyD,EAAG,OAAO,CAACS,YAAY,iBAAiBC,YAAY,CAAC,OAAS,oBAAoB,MAAQ,OAAO,QAAU,KAAKR,MAAM,CAAC,aAAaN,EAAIsD,UAAU,aAAatD,EAAIwB,UAAU,SAAWxB,EAAIiE,uBAAuBjD,GAAG,CAAC,iBAAmB,SAASoB,GAAQ,OAAOpC,EAAIkE,sCAAsClE,EAAIwD,MAAM,GAAGpD,EAAG,MAAM,CAACS,YAAY,eAAe,CAACT,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,OAAO,cAAc,OAAO,MAAQ,UAAU,cAAc,OAAO,YAAY,SAAS,CAACd,EAAIgC,GAAG,KAAKhC,EAAIiC,GAAGjC,EAAIe,GAAG,gBAAgBX,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,QAAU,OAAO,mBAAmB,yBAAyB,gBAAgB,OAAOyB,SAAS,CAAC,UAAYvC,EAAIiC,GAAGjC,EAAImE,kBAAkB,KAAKnE,EAAIwD,MAAM,IACr5S,EAAkB,GCDlB,EAAS,WAAa,IAAIxD,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,2BAA2BC,YAAY,CAAC,MAAQ,OAAO,OAAS,SAAS,CAACV,EAAG,MAAM,CAACS,YAAY,8BAA8BP,MAAM,CAAC,GAAKN,EAAIoE,YAAc,0BAC9Q,EAAkB,G,8HCStB,GACE7F,KAAM,OACN8F,WAAY,GACZC,MAAO,CACLC,SAAU,CACRzB,KAAM0B,MACNC,UAAU,EACVC,QAAS,WACP,MAAO,KAGXlD,UAAW,CACTsB,KAAM6B,QACND,SAAS,GAEXpB,UAAW,CACTR,KAAM8B,OACNF,QAAS,MAGbvI,KApBF,WAqBI,MAAO,CACL0I,UAAW,GACXC,SAAU,GACVV,YAAaW,KAAKC,SAASC,SAAS,IAAIC,OAAO,EAAG,GAClDC,QAAS,CACP,aAAgB,EAAxB,QACQ,UAAa,EAArB,QACQ,aAAgB,EAAxB,QACQ,SAAY,EAApB,QACQ,YAAe,EAAvB,QACQ,oBAAuB,EAA/B,QACQ,YAAe,EAAvB,QACQ,eAAkB,EAA1B,SAEMC,oBAAgBlC,EAChBmC,eAAe,EACfC,IAAI,IAAI,EAAd,GACA,KAAQ,MAAR,EAAQ,QAAR,EAAQ,aAAR,EAAQ,SAAR,IACA,KAAQ,UAAR,YACU,MAAO,2BACjB,iBAAY,SAAZ,SAAY,gBAAZ,UACA,qBAIEC,MAAO,CACLhB,SAAU,CACRiB,QADN,WACA,WACQC,YAAW,WACT,EAAV,mBAGMC,MAAM,EACNC,WAAW,IAGfC,UAzDF,WA0DQ3F,KAAKmF,iBACPnF,KAAKmF,eAAeS,aACpB5F,KAAKmF,oBAAiBlC,GAExBjD,KAAK4E,UAAUiB,SAAQ,SAA3B,GACMvE,EAAKwE,cAGTC,QAlEF,WAmEI,IAAJ,gEAEI/F,KAAKmF,eAAiB,IAAIa,kBAAiB,WAEzCC,EAAOC,UAAYD,EAAOE,aAAeF,EAAOG,gBAGlD,IAAJ,0CAEIpG,KAAKmF,eAAekB,QAAQJ,EAAQK,IAEtCC,QAAS,CACPC,YADJ,SACA,cACM,GAAI/E,GAASzB,KAAKsE,SAAS5H,OAMzB,OALAsD,KAAKyG,MAAM,yBACPzG,KAAKmF,iBACPnF,KAAKmF,eAAeS,aACpB5F,KAAKmF,oBAAiBlC,IAI1B,IAAN,mBAEM,GAAKyD,EAAQxK,MAAuC,IAA/BwK,EAAQxK,KAAKyK,OAAOjK,OAAzC,CAKA,IAAN,yBAEA,oDAEA,gEACMkK,EAAkBC,UAAYD,EAAkBC,UAAtD,uEAEA,EAFA,iKAKA,SALA,0EAMA,OANA,8DAQA,EARA,4EAWMrB,YAAW,WACT,IACE,GAAI,EAAd,WACY,IAAZ,6BACYsB,EAAiBD,UAAY,EAAzC,kBACY,EAAZ,qBACA,CACY,IAAZ,oBACcE,QAAS,CAAC,EAAxB,mBACc1D,UAAW,IAAM,EAA/B,UACc2D,YAAY,EACZC,YAAa,OACbC,WAAY,WACV,EAAhB,oBAEY,EAAZ,mBAEA,SACUC,QAAQC,IAAI,cAAeC,MAErC,QAxCQrH,KAAKwG,YAAY/E,EAAQ,IA0C7B6F,kBAvDJ,SAuDA,GACMtH,KAAK6E,SAAWvD,EAChBtB,KAAKoF,eAAgB,EACjBpF,KAAKuH,MAAM,cACbvH,KAAKuH,MAAMC,UAAUC,SAGzBC,cA9DJ,WA+DM,IAAN,gBACWC,GAAqC,KAA7BA,EAAKC,WAAW,IAAK,KAChC5H,KAAK6H,aAAaC,KAAK,cCzJ+S,I,kCCS1UC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCpBX,EAAS,WAAa,IAAIhI,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACS,YAAY,2BAA2BC,YAAY,CAAC,MAAQ,OAAO,OAAS,SAAS,CAACV,EAAG,MAAM,CAACS,YAAY,8BAA8BP,MAAM,CAAC,GAAKN,EAAIoE,YAAc,sBAAsB,CAAChE,EAAG,MAAM,CAACS,YAAY,4DAA4DgB,MAAO7B,EAAIiI,YAAYjI,EAAIkI,SAAU,CAAiB,iBAAflI,EAAIkI,OAA2B9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAA8BN,EAAIwD,KAAqB,cAAfxD,EAAIkI,OAAwB9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAA8BN,EAAIwD,KAAqB,iBAAfxD,EAAIkI,OAA2B9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAA8BN,EAAIwD,KAAqB,aAAfxD,EAAIkI,OAAuB9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAA6BN,EAAIwD,KAAqB,gBAAfxD,EAAIkI,OAA0B9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAAgCN,EAAIwD,KAAqB,wBAAfxD,EAAIkI,OAAkC9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAAwCN,EAAIwD,KAAqB,gBAAfxD,EAAIkI,OAA0B9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAAgCN,EAAIwD,KAAqB,mBAAfxD,EAAIkI,OAA6B9H,EAAG,MAAM,CAACS,YAAY,OAAOP,MAAM,CAAC,IAAM,EAAQ,WAAmCN,EAAIwD,KAAKpD,EAAG,OAAO,CAACU,YAAY,CAAC,YAAY,OAAO,MAAQ,UAAU,cAAc,SAAS,CAACd,EAAIgC,GAAGhC,EAAIiC,GAAGjC,EAAIkI,kBAC99C,EAAkB,GCyBtB,GACE3J,KAAM,UACN8F,WAAY,GACZC,MAAO,CACLC,SAAU,CACRzB,KAAM0B,MACNC,UAAU,EACVC,QAAS,WACP,MAAO,KAGXwD,OAAQ,CACNpF,KAAMqF,OACN1D,UAAU,EACVC,QAAS,IAEXlD,UAAW,CACTsB,KAAM6B,QACND,SAAS,GAEXpB,UAAW,CACTR,KAAM8B,OACNF,QAAS,MAGbvI,KAzBF,WA0BI,MAAO,CACL8L,YAAa,CACX,aAAgB,6BAChB,UAAa,6BACb,aAAgB,6BAChB,SAAY,6BACZ,YAAe,6BACf,oBAAuB,6BACvB,YAAe,6BACf,eAAkB,8BAEpBpD,UAAW,GACXT,YAAaW,KAAKC,SAASC,SAAS,IAAIC,OAAO,EAAG,GAClDkD,MAAO,GACPhD,oBAAgBlC,EAChBoC,IAAI,IAAI,EAAd,GACA,KAAQ,MAAR,EAAQ,QAAR,EAAQ,aAAR,EAAQ,SAAR,IACA,KACQ,UAAR,YACU,MAAV,2BACA,iBAAY,SAAZ,SAAY,gBAAZ,UACA,qBAKEC,MAAO,CACLhB,SAAU,CACRiB,QADN,WACA,WACQC,YAAW,WACL,EAAd,mBACY,EAAZ,mBAIMC,MAAM,EACNC,WAAW,IAGfK,QAjEF,WAkEI,IAAJ,gEAEI/F,KAAKmF,eAAiB,IAAIa,kBAAiB,WAEzCC,EAAOC,UAAYD,EAAOE,aAAeF,EAAOG,gBAGlD,IAAJ,0CAEIpG,KAAKmF,eAAekB,QAAQJ,EAAQK,IAEtCX,UA7EF,WA8EQ3F,KAAKmF,iBACPnF,KAAKmF,eAAeS,aACpB5F,KAAKmF,oBAAiBlC,GAExBjD,KAAK4E,UAAUiB,SAAQ,SAA3B,GACMvE,EAAKwE,cAGTS,QAAS,CACPC,YADJ,SACA,cACM,GAAI/E,GAASzB,KAAKsE,SAAS5H,OAMzB,OALAsD,KAAKyG,MAAM,yBACPzG,KAAKmF,iBACPnF,KAAKmF,eAAeS,aACpB5F,KAAKmF,oBAAiBlC,IAI1B,IAAN,mBAEM,GAAMyD,EAAQxK,MAAQwK,EAAQxK,KAAKyK,OAAnC,CAKA,IAAN,oDAEA,gEACMC,EAAkBC,UAAYD,EAAkBC,UAAtD,8LAGA,OAHA,8DAKA,EALA,0DAQMrB,YAAW,WACT,IACE,GAAI,EAAd,WACY,IAAZ,6BACYsB,EAAiBD,UAAY,EAAzC,kBACY,EAAZ,qBACA,CACY,IAAZ,oBACcE,QAAS,CAAC,EAAxB,mBACc1D,UAAW,IAAM,EAA/B,UACc2D,YAAY,EACZC,YAAa,OACbC,WAAY,WACV,EAAhB,oBAEY,EAAZ,mBAEA,SACUC,QAAQC,IAAI,cAAeC,MAErC,QAnCQrH,KAAKwG,YAAY/E,EAAQ,MC7HgT,ICS7U,G,oBAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,oBCmPf,GACE2G,QAAS,GACT/D,MAAO,CAAC,QACRD,WAAY,CAAd,kBACElI,KAJF,WAKI,MAAO,CACLmM,UAAW,GACX/D,SAAU,GACV/B,WAAY,GACZ+F,WAAY,EACZzG,cAAe,CACb,KAAQ,kBACR,KAAQ,kBACR,KAAQ,mBAEVwD,QAAIpC,EACJsF,YAAa,EACbnF,qBAAsB,GACtBI,kBAAmB,GACnBE,iBAAkB,GAClBC,qBAAsB,GACtBC,oBAAqB,GACrBC,4BAA6B,GAC7BC,oBAAqB,GACrBC,uBAAwB,GACxBC,sBAAuB,GACvBwE,cAAe,GACftE,OAAQ,GACRuE,aAAc,GACdC,gBAAiB,GACjBC,gBAAgB,EAChBjG,cAAc,EACdkG,WAAY,GACZC,eAAe,EACflG,WAAY,EACZmG,eAAW7F,EACX1B,WAAW,EACX8B,UAAW,IACX0F,OAAQ,GACRC,UAAW,GACX/H,MAAO,KAGXgI,SAAU,GACV3D,MAAO,CACL5E,KAAM,CACJ6E,QAAS,SAAf,gBACQ4B,QAAQC,IAAI,qBAAsB8B,GAClC/B,QAAQC,IAAI,mBAAoB+B,GAC5BA,GACFnJ,KAAKoJ,WAAU,WACb,EAAZ,0BACY,EAAZ,qCACY,EAAZ,wCACgB,EAAhB,0BACc,EAAd,0CAKM3D,MAAM,EACNC,WAAW,IAGfK,QAhEF,WAiEI/F,KAAKqF,IAAK,IAAI,EAAlB,GACA,KAAM,MAAN,EAAM,QAAN,EAAM,aAAN,EAAM,SAAN,IACA,KACM,UAAN,YACQ,MAAR,2BACA,iBAAU,SAAV,SAAU,gBAAV,UACA,oBAIEkB,QAAS,CACP/E,UADJ,WAEMxB,KAAKqJ,MAAMC,OAA+B,OAAtBtJ,KAAKqJ,MAAMC,OAAkB,KAAO,KACxDC,aAAaC,QAAQ,oBAAqBxJ,KAAKqJ,MAAMC,SAEvDtI,kBALJ,WAMM,IAAN,qBACM,EAAN,2BAEIoB,cATJ,SASA,GACMpC,KAAK6I,eAAgB,EACrB7I,KAAK2C,WAAa,EAClB3C,KAAK0C,cAAe,EACpB1C,KAAKyJ,sBAAsBnI,IAE7BgC,+BAfJ,WAeA,gCACMtD,KAAKuI,YAAc,EACnBvI,KAAKwD,mBAAX,kHACMxD,KAAK0D,kBAAX,iHACM1D,KAAK2D,sBAAX,qHACM3D,KAAK4D,qBAAX,oHACM5D,KAAK6D,6BAAX,4HACM7D,KAAK8D,qBAAX,oHACM9D,KAAK+D,wBAAX,uHAEM/D,KAAKuI,aAAevI,KAAKwD,kBAAkB9G,OAAS,EAAI,EAAI,EAC5DsD,KAAKuI,aAAevI,KAAK0D,iBAAiBhH,OAAS,EAAI,EAAI,EAC3DsD,KAAKuI,aAAevI,KAAK2D,qBAAqBjH,OAAS,EAAI,EAAI,EAC/DsD,KAAKuI,aAAevI,KAAK4D,oBAAoBlH,OAAS,EAAI,EAAI,EAC9DsD,KAAKuI,aAAevI,KAAK6D,4BAA4BnH,OAAS,EAAI,EAAI,EACtEsD,KAAKuI,aAAevI,KAAK8D,oBAAoBpH,OAAS,EAAI,EAAI,EAC9DsD,KAAKuI,aAAevI,KAAK+D,uBAAuBrH,OAAS,EAAI,EAAI,GAEnE+G,mBAjCJ,SAiCA,GAEA,OADMzD,KAAKuI,aAAexJ,EAChBiB,KAAKuI,aAAe,KACtBvI,KAAKgE,uBAAb,4EACQhE,KAAKkD,YAAY,KAGrBe,gCAxCJ,WAyCMjE,KAAKkD,YAAY,GACjBlD,KAAKkE,OAASlE,KAAKqF,GAAGqE,OAAO1J,KAAK4I,WAAW1E,QAAU,KAEzDuF,sBA5CJ,SA4CA,GACMzJ,KAAKoD,qBAAuB,GAC5BpD,KAAKwD,kBAAoB,GACzBxD,KAAK0D,iBAAmB,GACxB1D,KAAK2D,qBAAuB,GAC5B3D,KAAK4D,oBAAsB,GAC3B5D,KAAK6D,4BAA8B,GACnC7D,KAAK8D,oBAAsB,GAC3B9D,KAAK+D,uBAAyB,GAC9B/D,KAAKgE,sBAAwB,GAC7BhE,KAAK4I,WAAatH,EAClB6F,QAAQC,IAAIpH,KAAK4I,YACjB5I,KAAKoD,qBAAuBpD,KAAK4I,WAAWe,gBAAgBC,aAAatF,UAAY,GACrFtE,KAAK6I,eAAgB,GAEvBxG,cA3DJ,SA2DA,KACMrC,KAAKsI,UAAY7G,EACjBzB,KAAK+I,OAAS,GACd/I,KAAKyJ,sBAAsBnI,GAC3BtB,KAAKuC,WAAavC,KAAKqF,GAAGqE,OAAO1J,KAAK4I,WAAW1E,QAAU,KAU7DhB,YAzEJ,SAyEA,GACMlD,KAAK2C,WAAaA,EAClB,IAAN,0DACM3C,KAAK6J,yBAAyBC,EAAa9J,KAAK2C,aAElDkH,yBA9EJ,SA8EA,cACM,EAAN,gCACQrE,YAAW,WACL,EAAd,wBACY,EAAZ,0DAEA,OAGIrC,gBAvFJ,WAwFM,GAAInD,KAAKuH,MAAM,iBAAkB,CAC/B,IAAR,0DACQvH,KAAK2C,WAAaoH,SAAS/J,KAAKuH,MAAMyC,cAAc9D,UAAY4D,EAAa,QC5aqP,ICStU,G,oBAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCpBX,EAAS,WAAa,IAAI/J,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAwB,IAAtBJ,EAAIkK,eAAsB9J,EAAG,MAAM,CAACA,EAAG,KAAK,CAACS,YAAY,cAAc,CAACb,EAAIgC,GAAG,qBAAqB5B,EAAG,MAAM,CAACS,YAAY,YAAY,CAACb,EAAIgC,GAAGhC,EAAIiC,GAAGjC,EAAIkK,0BAAwChH,GAAlBlD,EAAImK,WAAyBnK,EAAIoK,GAAG,UAAU,KAAK,CAAC,KAAOpK,EAAImK,WAAWxJ,KAAK,SAAWX,EAAImK,WAAWE,WAAWrK,EAAIwD,MAAM,IACpZ,EAAkB,GCqBP,eAAI8G,OAAO,CACxB/L,KAAM,0BACNpC,KAAM,iBAAO,CACXgO,gBAAajH,EACbgH,eAAgB,KAElB1D,QAAS,CAKP+D,cAAe,SAASC,GACtB,IAAMC,EAAcD,EACpBvK,KAAKkK,WAAaM,EAAYC,OAC9BzK,KAAKiK,eAAiB,KAG1BlE,QAjBwB,WAoBtB,OAAU2E,OAAOC,iBACf,OAAUC,aACV5K,KAAKsK,eAEP,OAAUO,oBACV,OAAUC,kBAEZC,QA3BwB,WA4BtB,OAAUD,kBAEZnF,UA9BwB,WA+BtB,OAAU+E,OAAOM,oBACf,OAAUJ,aACV5K,KAAKsK,gBAGTW,cApCwB,SAoCVC,GACZlL,KAAKiK,eAAiB/B,OAAOgD,MC3DiX,ICQ9Y,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCIf,GACE5M,KAAM,MACN8F,WAAY,CAAd,8CCzB8T,ICQ1T,G,UAAY,eACd,EACA,EACAzD,GACA,EACA,KACA,KACA,OAIa,I,6FCnBA,GACbwK,eAAgB,KAChBC,aAAc,gBACdC,QAAS,WACTC,WAAY,OACZC,aAAc,OACdC,SAAU,OACVC,YAAa,KACbC,kBAAmB,SACnBC,aAAc,OACdC,WAAY,OACZC,eAAgB,KAChBC,aAAc,KACdC,WAAY,OACZC,qBAAsB,OACtBC,kBAAmB,OACnBC,SAAU,uDACVC,WAAY,OACZC,SAAU,iCACVC,WAAY,OACZC,SAAU,gCACVC,kBAAmB,OACnBC,mBAAoB,YACpBC,kBAAmB,QACnBC,kBAAmB,MCxBN,GACbvB,eAAgB,WAChBC,aAAc,yDACdC,QAAS,sBACTC,WAAY,eACZC,aAAc,eACdC,SAAU,gBACVC,YAAa,WACbC,kBAAmB,0BACnBC,aAAc,aACdC,WAAY,aACZW,kBAAmB,kBACnBV,eAAgB,WAChBG,qBAAsB,qBACtBC,kBAAmB,kBACnBH,aAAc,SACdC,WAAY,mBACZG,SAAU,qMACVC,WAAY,yBACZC,SAAU,8HACVC,WAAY,yBACZC,SAAU,uIACVE,mBAAoB,gDACpBC,kBAAmB,4BACnBC,kBAAmB,ICpBrBC,aAAIC,IAAIC,QAGR,IAAMC,EAAO,IAAID,OAAQ,CACvBvD,OAAQC,aAAawD,QAAQ,sBAAwB,KACrDzI,SAAU,CACR,GAAM0I,EACN,GAAMC,KAIKH,ICLf,aAAIF,IAAI,KACR,aAAItG,OAAO4G,eAAgB,EAE3B,IAAI,aAAI,CACNJ,KAAA,EACApD,OAAQ,SAAAyD,GAAC,OAAIA,EAAEC,MACdC,OAAO,S,4CChBVpP,EAAOD,QAAU,IAA0B,yC,kCCA3C,yBAAoc,EAAG,G,qBCAvcC,EAAOD,QAAU,IAA0B", "file": "js/app.9818c0ba.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/mem_robot.44e5fdbc.webp\";", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=0&lang=css&\"", "module.exports = \"data:image/png;base64,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\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=style&index=0&lang=scss&\"", "module.exports = __webpack_public_path__ + \"img/workload_robot.a4663857.webp\";", "module.exports = __webpack_public_path__ + \"img/index_robot.871d66b4.webp\";", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=style&index=1&id=becc3bae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=style&index=1&id=becc3bae&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/query_robot.3342da36.webp\";", "module.exports = __webpack_public_path__ + \"img/io_robot.1c748e65.webp\";", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=style&index=1&id=6f4f86a6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=style&index=1&id=6f4f86a6&lang=scss&scoped=true&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=1&id=bb906802&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=style&index=1&id=bb906802&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/cpu_robot.4c891857.webp\";", "// extracted by mini-css-extract-plugin\nmodule.exports = {\"menuText\":\"#999\",\"menuActiveText\":\"#41b584\",\"subMenuActiveText\":\"#41b584\",\"menuBg\":\"#fff\",\"menuHover\":\"#ecf8f3\",\"subMenuBg\":\"#fff\",\"subMenuHover\":\"#ecf8f3\"};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('WithStreamlitConnection',{scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar args = ref.args;\nreturn [_c('ReportComponent',{attrs:{\"args\":args.args || {}}})]}}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"c-flex-row\",staticStyle:{\"width\":\"100%\",\"font-size\":\"1rem\",\"line-height\":\"1.6!important\",\"height\":\"1000px\"}},[_c('div',{staticClass:\"c-flex-column\",staticStyle:{\"width\":\"55%\"}},[_c('div',{staticClass:\"c-flex-row c-align-items-center c-justify-content-between c-shaow-card\",staticStyle:{\"padding\":\"10px 20px\",\"border-radius\":\"80px!important\"}},[_c('el-form',{ref:\"form\",attrs:{\"inline\":true,\"label-position\":\"left\"}},[_c('el-form-item',{staticStyle:{\"margin-bottom\":\"0\"},attrs:{\"label\":_vm.$t('modelTip') + ':'}},[_c('el-select',{attrs:{\"placeholder\":\"\"},on:{\"change\":_vm.getAlertHistories},model:{value:(_vm.model),callback:function ($$v) {_vm.model=$$v},expression:\"model\"}},_vm._l((_vm.modelList),function(item){return _c('el-option',{key:item,attrs:{\"label\":item,\"value\":item}})}),1)],1)],1),_c('div',{staticClass:\"c-flex-row c-align-items-center\",staticStyle:{\"flex-shrink\":\"0\"}},[_c('el-switch',{staticStyle:{\"display\":\"block\"},attrs:{\"active-color\":\"#ff4949\",\"inactive-color\":\"#13ce66\",\"active-text\":\"\",\"inactive-text\":_vm.$t('playbackAnimationTip')},model:{value:(_vm.skipTyped),callback:function ($$v) {_vm.skipTyped=$$v},expression:\"skipTyped\"}}),_c('div',{staticClass:\"c-flex-row\",staticStyle:{\"margin-left\":\"20px\"},on:{\"click\":_vm.onEnClick}},[_c('img',{staticStyle:{\"width\":\"30px\",\"height\":\"30px\"},attrs:{\"src\":require(\"@/assets/ch_to_en.png\")}})])],1)],1),_c('div',{staticClass:\"c-flex c-flex-column\",staticStyle:{\"height\":\"calc(100vh - 100px)\",\"overflow-y\":\"auto\",\"margin\":\"10px 0\",\"padding\":\"0 10px\"}},_vm._l((_vm.historyMessages),function(item,index){return _c('div',{key:index,staticClass:\"diagnose-item c-flex-column\",staticStyle:{\"background\":\"RGBA(255, 255, 255, 1.00)\"}},[_c('div',{staticClass:\"c-flex-row c-align-items-center c-justify-content-between\"},[_c('div',{staticClass:\"c-flex-row c-align-items-center c-justify-content-left\"},[_vm._l((item.alerts),function(alert_item,alert_index){return _c('div',{key:alert_index,staticClass:\"c-flex-row c-justify-content-left\",staticStyle:{\"margin-right\":\"10px\"}},[_c('div',{style:(_vm.severityStyle[alert_item.alert_level])},[_vm._v(\" [\"+_vm._s(alert_item.alert_level)+_vm._s(alert_item.alert_level !== 'INFO' ? '🔥' : '')+\"] \")]),_c('div',{staticStyle:{\"color\":\"#666666\",\"margin-left\":\"5px\"}},[_vm._v(_vm._s(alert_item.alert_name))])])}),_c('div',{staticStyle:{\"color\":\"#999999\",\"font-size\":\"12px\",\"height\":\"12px\",\"line-height\":\"12px\",\"margin-top\":\"4px\"}},[_vm._v(_vm._s(item.report_generate_time))])],2),_c('div',{staticClass:\"c-flex-row c-align-items-center\"},[_c('el-button',{staticStyle:{\"margin\":\"0 10px\"},attrs:{\"type\":\"success\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.onReviewClick(item)}}},[_vm._v(_vm._s(_vm.$t('playbackButton'))),_c('i',{staticClass:\"el-icon-video-camera-solid el-icon--right\",staticStyle:{\"font-size\":\"16px\"}})]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.onReportClick(item, index)}}},[_vm._v(_vm._s(_vm.$t('reportButton'))),_c('i',{staticClass:\"el-icon-document-add el-icon--right\",staticStyle:{\"font-size\":\"16px\"}})])],1)])])}),0)]),_c('div',{staticClass:\"c-relative c-flex-column\",staticStyle:{\"overflow-y\":\"scroll\",\"height\":\"100vh\",\"overflow-x\":\"hidden\",\"width\":\"45%\",\"background\":\"RGBA(255, 255, 255, 1.00)\"}},[_c('div',{staticStyle:{\"background-color\":\"white\",\"padding\":\"0 10px\",\"margin\":\"0 10px\",\"border-radius\":\"8px\"},domProps:{\"innerHTML\":_vm._s(_vm.openReport)}})]),(_vm.reviewDrawer)?_c('el-drawer',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.reviewLoading),expression:\"reviewLoading\"}],attrs:{\"title\":_vm.$t('reviewDrawerTitle'),\"visible\":_vm.reviewDrawer,\"size\":\"95vw\",\"destroy-on-close\":\"\",\"direction\":\"rtl\"},on:{\"update:visible\":function($event){_vm.reviewDrawer=$event}}},[_c('div',{staticClass:\"c-relative c-flex-column\",staticStyle:{\"overflow\":\"hidden\",\"height\":\"100%\"}},[_c('el-steps',{staticStyle:{\"width\":\"100%\"},attrs:{\"active\":_vm.activeName,\"finish-status\":\"success\",\"simple\":\"\"}},[_c('el-step',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"title\":_vm.$t('setpTitle1')},nativeOn:{\"click\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"n\",undefined,$event.key,undefined)){ return null; }return _vm.onStepClick(0)}}}),_c('el-step',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"title\":_vm.$t('setpTitle2')},nativeOn:{\"click\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"n\",undefined,$event.key,undefined)){ return null; }return _vm.onStepClick(1)}}}),_c('el-step',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"title\":_vm.$t('setpTitle3')},nativeOn:{\"click\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"n\",undefined,$event.key,undefined)){ return null; }return _vm.onStepClick(2)}}})],1),_c('transition',{attrs:{\"name\":\"fade\"}},[_c('div',{ref:\"setpScrollDiv\",staticClass:\"c-relative c-flex-column\",staticStyle:{\"height\":\"calc(199 - 60px)\",\"overflow-y\":\"auto\",\"margin\":\"10px 0\"},on:{\"scroll\":_vm.stepScrollEvent}},[_c('div',{staticClass:\"review-step\"},[_c('div',{staticStyle:{\"height\":\"40px\",\"line-height\":\"40px\",\"color\":\"#333333\",\"font-weight\":\"bold\",\"font-size\":\"18px\"}},[_vm._v(\" 1.\"+_vm._s(_vm.$t('setpTip1'))+\" \")]),_c('div',{staticClass:\"c-flex-row c-align-items-center\",staticStyle:{\"height\":\"calc(100% - 40px)\"}},[(_vm.roleAssignerMessages.length > 0)?_c('OneChat',{key:\"RoleAssigner\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"width\":\"30%\"},attrs:{\"id\":\"RoleAssigner\",\"sender\":\"RoleAssigner\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.roleAssignerMessages},on:{\"playbackComplete\":function($event){return _vm.onRoleAssignerPlaybackComplete('0')}}}):_vm._e(),_c('div',{staticClass:\"c-flex-row\",staticStyle:{\"width\":\"70%\",\"height\":\"100%\"}},[(_vm.cpuExpertMessages.length > 0)?_c('OneChat',{key:\"CpuExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"CpuExpert\",\"sender\":\"CpuExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.cpuExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.ioExpertMessages.length > 0)?_c('OneChat',{key:\"IoExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"IoExpert\",\"sender\":\"IoExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.ioExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.memoryExpertMessages.length > 0)?_c('OneChat',{key:\"MemoryExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"MemoryExpert\",\"sender\":\"MemoryExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.memoryExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.indexExpertMessages.length > 0)?_c('OneChat',{key:\"IndexExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"IndexExpert\",\"sender\":\"IndexExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.indexExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.configurationExpertMessages.length > 0)?_c('OneChat',{key:\"ConfigurationExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"ConfigurationExpert\",\"sender\":\"ConfigurationExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.configurationExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.queryExpertMessages.length > 0)?_c('OneChat',{key:\"QueryExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"QueryExpert\",\"sender\":\"QueryExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.queryExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e(),(_vm.workloadExpertMessages.length > 0)?_c('OneChat',{key:\"WorkloadExpert\",staticClass:\"chat-container\",staticStyle:{\"height\":\"100%\",\"margin-left\":\"20px\",\"flex\":\"1 1 50%\"},attrs:{\"id\":\"WorkloadExpert\",\"sender\":\"WorkloadExpert\",\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.workloadExpertMessages},on:{\"playbackComplete\":function($event){return _vm.onPlaybackComplete(1)}}}):_vm._e()],1)],1)]),_c('div',{staticClass:\"review-step\"},[_c('span',{staticStyle:{\"height\":\"40px\",\"line-height\":\"40px\",\"color\":\"#333333\",\"font-weight\":\"bold\",\"margin\":\"10px 0\",\"font-size\":\"18px\"}},[_vm._v(\" 2.\"+_vm._s(_vm.$t('setpTip2'))+\" \")]),(_vm.brainstormingMessages.length > 0)?_c('Chat',{staticClass:\"chat-container\",staticStyle:{\"height\":\"calc(100% - 40px)\",\"width\":\"100%\",\"padding\":\"0\"},attrs:{\"type-speed\":_vm.typeSpeed,\"skip-typed\":_vm.skipTyped,\"messages\":_vm.brainstormingMessages},on:{\"playbackComplete\":function($event){return _vm.onBrainstormingPlaybackComplete()}}}):_vm._e()],1),_c('div',{staticClass:\"review-step\"},[_c('span',{staticStyle:{\"height\":\"40px\",\"line-height\":\"40px\",\"color\":\"#333333\",\"font-weight\":\"bold\",\"font-size\":\"18px\"}},[_vm._v(\"3.\"+_vm._s(_vm.$t('setpTip3')))]),_c('div',{staticStyle:{\"width\":\"100%\",\"padding\":\"10px\",\"background-color\":\"RGBA(242, 246, 255, 1)\",\"border-radius\":\"8px\"},domProps:{\"innerHTML\":_vm._s(_vm.report)}})])])])],1)]):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"c-flex-column c-relative\",staticStyle:{\"width\":\"100%\",\"height\":\"100%\"}},[_c('div',{staticClass:\"scroll-container c-relative\",attrs:{\"id\":_vm.componentId + '-scroll-container'}})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"c-flex-column c-relative\" style=\"width: 100%; height: 100%;\">\n    <div :id=\"componentId + '-scroll-container'\" class=\"scroll-container c-relative\" />\n  </div>\n</template>\n\n<script>\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\nimport Typed from 'typed.js'\nexport default {\n  name: 'Chat',\n  components: { },\n  props: {\n    messages: {\n      type: Array,\n      required: true,\n      default: function() {\n        return []\n      }\n    },\n    skipTyped: {\n      type: Boolean,\n      default: false\n    },\n    typeSpeed: {\n      type: Number,\n      default: 100\n    }\n  },\n  data() {\n    return {\n      typedObjs: [],\n      chatText: '',\n      componentId: Math.random().toString(36).substr(2, 9),\n      faceMap: {\n        'RoleAssigner': require('@/assets/dba_robot.webp'),\n        'CpuExpert': require('@/assets/cpu_robot.webp'),\n        'MemoryExpert': require('@/assets/mem_robot.webp'),\n        'IoExpert': require('@/assets/io_robot.webp'),\n        'IndexExpert': require('@/assets/index_robot.webp'),\n        'ConfigurationExpert': require('@/assets/configuration_robot.webp'),\n        'QueryExpert': require('@/assets/query_robot.webp'),\n        'WorkloadExpert': require('@/assets/workload_robot.webp')\n      },\n      scrollObserver: undefined,\n      phraseVisible: false,\n      md: new MarkdownIt()\n        .set({ html: true, breaks: true, typographer: true, linkify: true })\n        .set({ highlight: function(code) {\n          return '<pre class=\"hljs\"><code>' +\n              hljs.highlight(code, { language: 'python', ignoreIllegals: true }).value +\n              '</code></pre>'\n        } })\n    }\n  },\n  watch: {\n    messages: {\n      handler() {\n        setTimeout(() => {\n          this.dealMessage(0)\n        })\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  destroyed() {\n    if (this.scrollObserver) {\n      this.scrollObserver.disconnect()\n      this.scrollObserver = undefined\n    }\n    this.typedObjs.forEach(item => {\n      item.destroy()\n    })\n  },\n  mounted() {\n    const target = document.getElementById(this.componentId + '-scroll-container')\n    // 创建MutationObserver对象\n    this.scrollObserver = new MutationObserver(function() {\n      // 处理高度变化操作\n      target.scrollTop = target.scrollHeight - target.clientHeight\n    })\n    // 配置观察选项\n    const config = { attributes: true, childList: true, subtree: true }\n    // 开始观察目标节点\n    this.scrollObserver.observe(target, config)\n  },\n  methods: {\n    dealMessage(index) {\n      if (index >= this.messages.length) {\n        this.$emit('playbackComplete')\n        if (this.scrollObserver) {\n          this.scrollObserver.disconnect()\n          this.scrollObserver = undefined\n        }\n        return\n      }\n      const message = this.messages[index]\n\n      if (!message.data || message.data.trim().length === 0) {\n        this.dealMessage(index + 1)\n        return\n      }\n\n      const faceImage = this.faceMap[message.sender]\n\n      const divId = 'message-' + Math.random().toString(36).substr(2, 9)\n\n      const messagesContainer = document.getElementById(this.componentId + '-scroll-container')\n      messagesContainer.innerHTML = messagesContainer.innerHTML +\n        `<div class=\"text-item c-flex-row left\">\n          <img src=\"${faceImage}\" class=\"face\">\n          <div class=\"c-flex-column\">\n            <span style=\"font-size: 1rem; color: #333333; margin-bottom: 5px\">\n              ${message.sender}\n              <span style=\"margin-left: 5px; color: #666666\">${message.time}</span>\n            </span>\n            <div id=\"${divId}\" class=\"content c-flex-column\"></div>\n          </div>\n        </div>`\n      setTimeout(() => {\n        try {\n          if (this.skipTyped) {\n            const contentContainer = document.getElementById(divId)\n            contentContainer.innerHTML = this.md.render(message.data)\n            this.dealMessage(index + 1)\n          } else {\n            const typedObj = new Typed('#' + divId, {\n              strings: [this.md.render(message.data)],\n              typeSpeed: 100 - this.typeSpeed,\n              showCursor: false,\n              contentType: 'html',\n              onComplete: () => {\n                this.dealMessage(index + 1)\n              } })\n            this.typedObjs.push(typedObj)\n          }\n        } catch (e) {\n          console.log('Typed Error', e)\n        }\n      }, 0)\n    },\n    onPhraseItemClick(item) {\n      this.chatText = item\n      this.phraseVisible = false\n      if (this.$refs['chatInput']) {\n        this.$refs.chatInput.focus()\n      }\n    },\n    onChatConfirm() {\n      const text = this.chatText\n      if (!text || text.replaceAll(' ', '') === '') {\n        this.$onceMessage.info('不能发送空消息')\n      }\n    }\n  }\n\n}\n</script>\n\n<style lang=\"scss\">\n\n.text-item {\n  margin: 20px 0;\n  align-items: flex-start;\n  justify-content: flex-start;\n\n.face {\n  width: 40px;\n  height: 40px;\n  border-radius: 40px;\n  margin-right: 7px;\n}\n\n.content {\n  color: #111111;\n  font-size: 14px;\n  min-height: 20px;\n  border-radius: 20px;\n  padding: 6px 12px;\n  line-height: 20px;\n  background-color: #ffffff;\n  word-break: break-all;\n  word-wrap: break-word;\n  position: relative;\n}\n}\n\n</style>\n\n<style lang=\"scss\" scoped>\n\n.bottom-input-container {\n  background: #ffffff;\n  bottom: 20px;\n  right: 20px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  position: absolute;\n}\n\n.scroll-container {\n  transition: all 0.1s ease;\n  overflow-y: auto;\n  overflow-x: hidden;\n  position: relative;\n  padding-bottom: 20px;\n  width: calc(100% - 20px);\n  padding-left: 10px;\n  padding-right: 10px;\n  height: 100%;\n\n  .item-space {\n    height: 15px;\n  }\n\n  .time {\n    color: #666;\n    font-size: 12px;\n    text-align: center;\n    margin-bottom: 10px;\n  }\n\n  .time-item {\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 10px;\n    font-size: 15px;\n    color: #9d9d9d;\n  }\n}\n</style>\n\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chat.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Chat.vue?vue&type=template&id=becc3bae&scoped=true&\"\nimport script from \"./Chat.vue?vue&type=script&lang=js&\"\nexport * from \"./Chat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Chat.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./Chat.vue?vue&type=style&index=1&id=becc3bae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"becc3bae\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"c-flex-column c-relative\",staticStyle:{\"width\":\"100%\",\"height\":\"100%\"}},[_c('div',{staticClass:\"scroll-container c-relative\",attrs:{\"id\":_vm.componentId + '-scroll-container'}},[_c('div',{staticClass:\"message-header c-relative c-flex-row c-align-items-center\",style:(_vm.headerStyle[_vm.sender])},[(_vm.sender === 'RoleAssigner')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/dba_robot.webp\")}}):_vm._e(),(_vm.sender === 'CpuExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/cpu_robot.webp\")}}):_vm._e(),(_vm.sender === 'MemoryExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/mem_robot.webp\")}}):_vm._e(),(_vm.sender === 'IoExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/io_robot.webp\")}}):_vm._e(),(_vm.sender === 'IndexExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/index_robot.webp\")}}):_vm._e(),(_vm.sender === 'ConfigurationExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/configuration_robot.webp\")}}):_vm._e(),(_vm.sender === 'QueryExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/query_robot.webp\")}}):_vm._e(),(_vm.sender === 'WorkloadExpert')?_c('img',{staticClass:\"face\",attrs:{\"src\":require(\"@/assets/workload_robot.webp\")}}):_vm._e(),_c('span',{staticStyle:{\"font-size\":\"16px\",\"color\":\"#FFFFFF\",\"margin-left\":\"10px\"}},[_vm._v(_vm._s(_vm.sender))])])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"c-flex-column c-relative\" style=\"width: 100%; height: 100%;\">\n    <div :id=\"componentId + '-scroll-container'\" class=\"scroll-container c-relative\">\n      <div\n        class=\"message-header c-relative c-flex-row c-align-items-center\"\n        :style=\"headerStyle[sender]\"\n      >\n        <img v-if=\"sender === 'RoleAssigner'\" src=\"@/assets/dba_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'CpuExpert'\" src=\"@/assets/cpu_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'MemoryExpert'\" src=\"@/assets/mem_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'IoExpert'\" src=\"@/assets/io_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'IndexExpert'\" src=\"@/assets/index_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'ConfigurationExpert'\" src=\"@/assets/configuration_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'QueryExpert'\" src=\"@/assets/query_robot.webp\" class=\"face\">\n        <img v-if=\"sender === 'WorkloadExpert'\" src=\"@/assets/workload_robot.webp\" class=\"face\">\n        <span style=\"font-size: 16px; color: #FFFFFF; margin-left: 10px\">{{ sender }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\nimport Typed from 'typed.js'\n\nexport default {\n  name: 'OneChat',\n  components: {},\n  props: {\n    messages: {\n      type: Array,\n      required: true,\n      default: function() {\n        return []\n      }\n    },\n    sender: {\n      type: String,\n      required: true,\n      default: ''\n    },\n    skipTyped: {\n      type: Boolean,\n      default: false\n    },\n    typeSpeed: {\n      type: Number,\n      default: 100\n    }\n  },\n  data() {\n    return {\n      headerStyle: {\n        'RoleAssigner': 'background-color: #01B77E;',\n        'CpuExpert': 'background-color: #0F2F5F;',\n        'MemoryExpert': 'background-color: #FB9996;',\n        'IoExpert': 'background-color: #7649af;',\n        'IndexExpert': 'background-color: #ecb42b;',\n        'ConfigurationExpert': 'background-color: #67C23A;',\n        'QueryExpert': 'background-color: #FFC2E3;',\n        'WorkloadExpert': 'background-color: #D51374;'\n      },\n      typedObjs: [],\n      componentId: Math.random().toString(36).substr(2, 9),\n      chats: [],\n      scrollObserver: undefined,\n      md: new MarkdownIt()\n        .set({ html: true, breaks: true, typographer: true, linkify: true })\n        .set({\n          highlight: function(code) {\n            return '<pre class=\"hljs\"><code>' +\n              hljs.highlight(code, { language: 'python', ignoreIllegals: true }).value +\n              '</code></pre>'\n          }\n        })\n    }\n  },\n  watch: {\n    messages: {\n      handler() {\n        setTimeout(() => {\n          if (this.messages.length > 0) {\n            this.dealMessage(0)\n          }\n        })\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n    const target = document.getElementById(this.componentId + '-scroll-container')\n    // 创建MutationObserver对象\n    this.scrollObserver = new MutationObserver(function() {\n      // 处理高度变化操作\n      target.scrollTop = target.scrollHeight - target.clientHeight\n    })\n    // 配置观察选项\n    const config = { attributes: true, childList: true, subtree: true }\n    // 开始观察目标节点\n    this.scrollObserver.observe(target, config)\n  },\n  destroyed() {\n    if (this.scrollObserver) {\n      this.scrollObserver.disconnect()\n      this.scrollObserver = undefined\n    }\n    this.typedObjs.forEach(item => {\n      item.destroy()\n    })\n  },\n  methods: {\n    dealMessage(index) {\n      if (index >= this.messages.length) {\n        this.$emit('playbackComplete')\n        if (this.scrollObserver) {\n          this.scrollObserver.disconnect()\n          this.scrollObserver = undefined\n        }\n        return\n      }\n      const message = this.messages[index]\n\n      if (!(message.data && message.data.trim())) {\n        this.dealMessage(index + 1)\n        return\n      }\n\n      const divId = 'message-' + Math.random().toString(36).substr(2, 9)\n\n      const messagesContainer = document.getElementById(this.componentId + '-scroll-container')\n      messagesContainer.innerHTML = messagesContainer.innerHTML +\n        `<div class=\"text-item c-flex-column\">\n            <span style=\"font-size: 1rem; color: #333333; margin-bottom: 5px\">\n              <span style=\"margin-left: 5px; color: #666666\">${message.time}</span>\n            </span>\n            <div id=\"${divId}\" class=\"content c-flex-column\"></div>\n        </div>`\n\n      setTimeout(() => {\n        try {\n          if (this.skipTyped) {\n            const contentContainer = document.getElementById(divId)\n            contentContainer.innerHTML = this.md.render(message.data)\n            this.dealMessage(index + 1)\n          } else {\n            const typedObj = new Typed('#' + divId, {\n              strings: [this.md.render(message.data)],\n              typeSpeed: 100 - this.typeSpeed,\n              showCursor: false,\n              contentType: 'html',\n              onComplete: () => {\n                this.dealMessage(index + 1)\n              } })\n            this.typedObjs.push(typedObj)\n          }\n        } catch (e) {\n          console.log('Typed Error', e)\n        }\n      }, 0)\n    }\n  }\n}\n</script>\n\n<style>\n.text-item {\n  margin: 20px 10px;\n  align-items: flex-start;\n  justify-content: flex-start;\n  word-break: break-all;\n  word-wrap: break-word;\n  overflow-x: scroll;\n}\n\n.content {\n  color: #333333;\n  font-size: 14px;\n  min-height: 20px;\n  border-radius: 20px;\n  padding: 6px 12px;\n  line-height: 20px;\n  background-color: #ffffff;\n  word-break: break-all;\n  word-wrap: break-word;\n  position: relative;\n}\n\n.json-viewer {\n  width: 100%;\n}\n\n</style>\n\n<style lang=\"scss\" scoped>\n\n.json-viewer {\n  width: 100%;\n}\n\n.bottom-input-container {\n  background: #ffffff;\n  bottom: 20px;\n  right: 20px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  position: absolute;\n}\n\n.message-header {\n  position: sticky;\n  top: 0;\n  width: 100%;\n  z-index: 10;\n  padding: 5px 10px\n}\n\n.scroll-container {\n  transition: all 0.1s ease;\n  overflow-y: auto;\n  overflow-x: hidden;\n  position: relative;\n  padding-bottom: 20px;\n  width: 100%;\n  height: 100%;\n  box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.1);\n\n  .item-space {\n    height: 15px;\n  }\n\n  .time {\n    color: #666;\n    font-size: 12px;\n    text-align: center;\n    margin-bottom: 10px;\n  }\n\n  .time-item {\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 10px;\n    font-size: 15px;\n    color: #9d9d9d;\n  }\n\n  .face {\n    width: 40px;\n    height: 40px;\n    border-radius: 40px;\n    margin-right: 7px;\n  }\n}\n</style>\n\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OneChat.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OneChat.vue?vue&type=template&id=bb906802&scoped=true&\"\nimport script from \"./OneChat.vue?vue&type=script&lang=js&\"\nexport * from \"./OneChat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OneChat.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./OneChat.vue?vue&type=style&index=1&id=bb906802&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bb906802\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"c-flex-row\" style=\"width: 100%; font-size: 1rem; line-height: 1.6!important; height: 1000px\">\n    <div class=\"c-flex-column\" style=\" width: 55%;\">\n      <div\n        class=\"c-flex-row c-align-items-center c-justify-content-between c-shaow-card\"\n        style=\"padding: 10px 20px; border-radius: 80px!important;\"\n      >\n        <el-form\n          ref=\"form\"\n          :inline=\"true\"\n          label-position=\"left\"\n        >\n          <el-form-item :label=\"$t('modelTip') + ':'\" style=\"margin-bottom: 0\">\n            <el-select v-model=\"model\" placeholder=\"\" @change=\"getAlertHistories\">\n              <el-option\n                v-for=\"item in modelList\"\n                :key=\"item\"\n                :label=\"item\"\n                :value=\"item\"\n              />\n            </el-select>\n          </el-form-item>\n\n        </el-form>\n\n        <div class=\"c-flex-row c-align-items-center\" style=\"flex-shrink: 0\">\n          <el-switch\n            v-model=\"skipTyped\"\n            style=\"display: block\"\n            active-color=\"#ff4949\"\n            inactive-color=\"#13ce66\"\n            active-text=\"\"\n            :inactive-text=\"$t('playbackAnimationTip')\"\n          />\n          <div class=\"c-flex-row\" style=\"margin-left: 20px\" @click=\"onEnClick\">\n            <img src=\"@/assets/ch_to_en.png\" style=\"width: 30px; height: 30px;\">\n          </div>\n        </div>\n\n      </div>\n      <div class=\"c-flex c-flex-column\" style=\"height: calc(100vh - 100px); overflow-y: auto; margin: 10px 0; padding: 0 10px\">\n        <div\n          v-for=\"(item, index) in historyMessages\"\n          :key=\"index\"\n          class=\"diagnose-item c-flex-column\"\n          style=\"background: RGBA(255, 255, 255, 1.00);\"\n        >\n          <div class=\"c-flex-row c-align-items-center c-justify-content-between\">\n            <div class=\"c-flex-row c-align-items-center c-justify-content-left\">\n              <div v-for=\"(alert_item, alert_index) in item.alerts\" :key=\"alert_index\" class=\"c-flex-row c-justify-content-left\" style=\"margin-right: 10px\">\n                <div :style=\"severityStyle[alert_item.alert_level]\">\n                  [{{ alert_item.alert_level }}{{ alert_item.alert_level !== 'INFO' ? '🔥' : '' }}]\n                </div>\n                <div style=\"color: #666666; margin-left: 5px\">{{ alert_item.alert_name }}</div>\n              </div>\n              <div style=\"color: #999999; font-size: 12px; height: 12px; line-height: 12px; margin-top: 4px\">{{ item.report_generate_time }}</div>\n            </div>\n            <div class=\"c-flex-row c-align-items-center\">\n              <el-button type=\"success\" size=\"small\" style=\"margin:0 10px\" @click=\"onReviewClick(item)\">{{ $t('playbackButton') }}<i\n                class=\"el-icon-video-camera-solid el-icon--right\"\n                style=\"font-size: 16px\"\n              />\n              </el-button>\n              <el-button type=\"warning\" size=\"small\" @click=\"onReportClick(item, index)\">{{ $t('reportButton') }}<i\n                class=\"el-icon-document-add el-icon--right\"\n                style=\"font-size: 16px\"\n              /></el-button>\n            </div>\n          </div>\n<!--          <el-collapse-transition>-->\n<!--            <el-carousel-->\n<!--              v-if=\"openIndex === index && charts.length > 0\"-->\n<!--              :interval=\"3000\"-->\n<!--              arrow=\"always\"-->\n<!--              height=\"260\"-->\n<!--              style=\"background: RGBA(255, 255, 255, 1.00); padding: 10px; margin: 10px; border-radius: 8px;\"-->\n<!--            >-->\n<!--              <el-carousel-item v-for=\"(chartItem, chartIndex) in charts\" :key=\"chartIndex\">-->\n<!--                <lineChart-->\n<!--                  style=\"height: 200px; width: calc(100% - 40px);\"-->\n<!--                  :chart-option=\"chartItem\"-->\n<!--                />-->\n<!--              </el-carousel-item>-->\n<!--            </el-carousel>-->\n<!--          </el-collapse-transition>-->\n        </div>\n      </div>\n    </div>\n    <div\n      class=\"c-relative c-flex-column\"\n      style=\"overflow-y: scroll; height: 100vh; overflow-x: hidden; width: 45%; background: RGBA(255, 255, 255, 1.00);\"\n    >\n      <div\n        style=\"background-color: white; padding: 0 10px; margin: 0 10px; border-radius: 8px;\"\n        v-html=\"openReport\"\n      />\n    </div>\n    <el-drawer\n      v-if=\"reviewDrawer\"\n      v-loading=\"reviewLoading\"\n      :title=\"$t('reviewDrawerTitle')\"\n      :visible.sync=\"reviewDrawer\"\n      size=\"95vw\"\n      destroy-on-close\n      direction=\"rtl\"\n    >\n      <div class=\"c-relative c-flex-column\" style=\"overflow: hidden; height: 100%\">\n        <el-steps :active=\"activeName\" finish-status=\"success\" simple style=\"width: 100%;\">\n          <el-step :title=\"$t('setpTitle1')\" style=\"cursor: pointer\" @click.n.native=\"onStepClick(0)\" />\n          <el-step :title=\"$t('setpTitle2')\" style=\"cursor: pointer\" @click.n.native=\"onStepClick(1)\" />\n          <el-step :title=\"$t('setpTitle3')\" style=\"cursor: pointer\" @click.n.native=\"onStepClick(2)\" />\n        </el-steps>\n\n        <transition name=\"fade\">\n          <div\n            ref=\"setpScrollDiv\"\n            class=\"c-relative c-flex-column\"\n            style=\"height: calc(199 - 60px); overflow-y: auto; margin: 10px 0\"\n            @scroll=\"stepScrollEvent\"\n          >\n            <div class=\"review-step\">\n              <div style=\"height: 40px; line-height: 40px; color: #333333; font-weight: bold; font-size: 18px\">\n                1.{{ $t('setpTip1') }}\n              </div>\n              <div class=\"c-flex-row c-align-items-center\" style=\"height: calc(100% - 40px)\">\n                <OneChat\n                  v-if=\"roleAssignerMessages.length > 0\"\n                  id=\"RoleAssigner\"\n                  key=\"RoleAssigner\"\n                  class=\"chat-container\"\n                  sender=\"RoleAssigner\"\n                  :type-speed=\"typeSpeed\"\n                  :skip-typed=\"skipTyped\"\n                  :messages=\"roleAssignerMessages\"\n                  style=\"height: 100%; width: 30%;\"\n                  @playbackComplete=\"onRoleAssignerPlaybackComplete('0')\"\n                />\n                <div class=\"c-flex-row\" style=\"width: 70%; height: 100%;\">\n                  <OneChat\n                    v-if=\"cpuExpertMessages.length > 0\"\n                    id=\"CpuExpert\"\n                    key=\"CpuExpert\"\n                    sender=\"CpuExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"cpuExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"ioExpertMessages.length > 0\"\n                    id=\"IoExpert\"\n                    key=\"IoExpert\"\n                    sender=\"IoExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"ioExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"memoryExpertMessages.length > 0\"\n                    id=\"MemoryExpert\"\n                    key=\"MemoryExpert\"\n                    sender=\"MemoryExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"memoryExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"indexExpertMessages.length > 0\"\n                    id=\"IndexExpert\"\n                    key=\"IndexExpert\"\n                    sender=\"IndexExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"indexExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"configurationExpertMessages.length > 0\"\n                    id=\"ConfigurationExpert\"\n                    key=\"ConfigurationExpert\"\n                    sender=\"ConfigurationExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"configurationExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"queryExpertMessages.length > 0\"\n                    id=\"QueryExpert\"\n                    key=\"QueryExpert\"\n                    sender=\"QueryExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"queryExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                  <OneChat\n                    v-if=\"workloadExpertMessages.length > 0\"\n                    id=\"WorkloadExpert\"\n                    key=\"WorkloadExpert\"\n                    sender=\"WorkloadExpert\"\n                    :type-speed=\"typeSpeed\"\n                    :skip-typed=\"skipTyped\"\n                    class=\"chat-container\"\n                    :messages=\"workloadExpertMessages\"\n                    style=\"height: 100%; margin-left: 20px; flex: 1 1 50%;\"\n                    @playbackComplete=\"onPlaybackComplete(1)\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div class=\"review-step\">\n              <span style=\"height: 40px; line-height: 40px; color: #333333; font-weight: bold; margin: 10px 0; font-size: 18px\">\n                2.{{ $t('setpTip2') }}\n              </span>\n              <Chat\n                v-if=\"brainstormingMessages.length > 0\"\n                class=\"chat-container\"\n                :type-speed=\"typeSpeed\"\n                :skip-typed=\"skipTyped\"\n                :messages=\"brainstormingMessages\"\n                style=\"height: calc(100% - 40px); width: 100%; padding: 0\"\n                @playbackComplete=\"onBrainstormingPlaybackComplete()\"\n              />\n            </div>\n\n            <div class=\"review-step\">\n              <span style=\"height: 40px; line-height: 40px; color: #333333; font-weight: bold; font-size: 18px\">3.{{ $t('setpTip3') }}</span>\n              <div style=\"width: 100%; padding: 10px; background-color: RGBA(242, 246, 255, 1); border-radius: 8px\" v-html=\"report\" />\n            </div>\n          </div>\n        </transition>\n      </div>\n    </el-drawer>\n  </div>\n</template>\n\n<script>\n\nimport Vue from 'vue'\nimport Chat from '@/components/Chat'\nimport OneChat from '@/components/OneChat'\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\n// import lineChart from '@/components/echarts/vue-chart'\n// import { lineChartOption } from '@/utils/echart-ori-options'\nimport { Streamlit } from \"streamlit-component-lib\";\n\nexport default {\n  filters: {},\n  props: [\"args\"],\n  components: { OneChat, Chat },\n  data() {\n    return {\n      timeRange: [],\n      messages: [],\n      openReport: '',\n      openIndex: -1,\n      severityStyle: {\n        'CRIT': 'color: #F56C6C;',\n        'WARN': 'color: #E6A23C;',\n        'INFO': 'color: #909399;'\n      },\n      md: undefined,\n      expertCount: 0,\n      roleAssignerMessages: [],\n      cpuExpertMessages: [],\n      ioExpertMessages: [],\n      memoryExpertMessages: [],\n      indexExpertMessages: [],\n      configurationExpertMessages: [],\n      queryExpertMessages: [],\n      workloadExpertMessages: [],\n      brainstormingMessages: [],\n      tableMessages: [],\n      report: '',\n      introMessage: [],\n      historyMessages: [],\n      historyLoading: false,\n      reviewDrawer: false,\n      reviewItem: {},\n      reviewLoading: false,\n      activeName: 0,\n      analyseAt: undefined,\n      skipTyped: true,\n      typeSpeed: 100,\n      charts: [],\n      modelList: [],\n      model: ''\n    }\n  },\n  computed: {},\n  watch: {\n    args: {\n      handler: function(val, oldVal) {\n        console.log('args changed from:', oldVal)\n        console.log('args changed to:', val)\n        if (val) {\n          this.$nextTick(() => {\n            this.modelList = val.modelList || []\n            this.model = val.currentModel || this.modelList[0]\n            this.historyMessages = val.diagnoseHistories || []\n            if (this.historyMessages.length > 0) {\n              this.onReportClick(this.historyMessages[0], 0)\n            }\n          })\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n    this.md = new MarkdownIt()\n        .set({ html: true, breaks: true, typographer: true, linkify: true })\n        .set({\n          highlight: function(code) {\n            return '<pre class=\"hljs\"><code>' +\n                hljs.highlight(code, { language: 'python', ignoreIllegals: true }).value +\n                '</code></pre>'\n          }\n        })\n  },\n  methods: {\n    onEnClick() {\n      this.$i18n.locale = this.$i18n.locale === 'en' ? 'zh' : 'en'\n      localStorage.setItem('LanguageSwitching', this.$i18n.locale)\n    },\n    getAlertHistories() {\n      const data = {model: this.model}\n      Streamlit.setComponentValue(data)\n    },\n    onReviewClick(item) {\n      this.reviewLoading = true\n      this.activeName = 0\n      this.reviewDrawer = true\n      this.getAlertHistoryDetail(item)\n    },\n    onRoleAssignerPlaybackComplete() {\n      this.expertCount = 0\n      this.cpuExpertMessages = this.reviewItem.anomalyAnalysis?.CpuExpert?.messages || []\n      this.ioExpertMessages = this.reviewItem.anomalyAnalysis?.IoExpert?.messages || []\n      this.memoryExpertMessages = this.reviewItem.anomalyAnalysis?.MemoryExpert?.messages || []\n      this.indexExpertMessages = this.reviewItem.anomalyAnalysis?.IndexExpert?.messages || []\n      this.configurationExpertMessages = this.reviewItem.anomalyAnalysis?.ConfigurationExpert?.messages || []\n      this.queryExpertMessages = this.reviewItem.anomalyAnalysis?.QueryExpert?.messages || []\n      this.workloadExpertMessages = this.reviewItem.anomalyAnalysis?.WorkloadExpert?.messages || []\n\n      this.expertCount += this.cpuExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.ioExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.memoryExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.indexExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.configurationExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.queryExpertMessages.length > 0 ? 1 : 0\n      this.expertCount += this.workloadExpertMessages.length > 0 ? 1 : 0\n    },\n    onPlaybackComplete(value) {\n      this.expertCount -= value\n      if (this.expertCount <= 0) {\n        this.brainstormingMessages = this.reviewItem.brainstorming?.messages || []\n        this.onStepClick(1)\n      }\n    },\n    onBrainstormingPlaybackComplete() {\n      this.onStepClick(2)\n      this.report = this.md.render(this.reviewItem.report || '')\n    },\n    getAlertHistoryDetail(item) {\n      this.roleAssignerMessages = []\n      this.cpuExpertMessages = []\n      this.ioExpertMessages = []\n      this.memoryExpertMessages = []\n      this.indexExpertMessages = []\n      this.configurationExpertMessages = []\n      this.queryExpertMessages = []\n      this.workloadExpertMessages = []\n      this.brainstormingMessages = []\n      this.reviewItem = item\n      console.log(this.reviewItem)\n      this.roleAssignerMessages = this.reviewItem.anomalyAnalysis.RoleAssigner.messages || []\n      this.reviewLoading = false\n    },\n    onReportClick(item, index) {\n      this.openIndex = index\n      this.charts = []\n      this.getAlertHistoryDetail(item)\n      this.openReport = this.md.render(this.reviewItem.report || '')\n      // const topMetrics = this.reviewItem.topMetrics || []\n      // topMetrics.forEach(item => {\n      //   const option = JSON.parse(JSON.stringify(lineChartOption))\n      //   option.series[0].data = item.values\n      //   option.title.text = item.title\n      //   option.color = option.color[Math.floor(Math.random() * option.color.length)]\n      //   this.charts.push(option)\n      // })\n    },\n    onStepClick(activeName) {\n      this.activeName = activeName\n      const calcHeight = this.$refs.setpScrollDiv.getBoundingClientRect().height\n      this.scrollToTopWithAnimation(calcHeight * this.activeName)\n    },\n    scrollToTopWithAnimation(scrollTop) {\n      Vue.nextTick(() => {\n        setTimeout(() => {\n          if (this.$refs['setpScrollDiv']) {\n            this.$refs.setpScrollDiv.scrollTo({ top: scrollTop, behavior: 'smooth' })\n          }\n        }, 0)\n      })\n    },\n    stepScrollEvent() {\n      if (this.$refs['setpScrollDiv']) {\n        const calcHeight = this.$refs.setpScrollDiv.getBoundingClientRect().height\n        this.activeName = parseInt(this.$refs.setpScrollDiv.scrollTop / calcHeight + 0.5)\n      }\n    }\n  }\n}\n</script>\n\n<style>\n\n.hljs {\n  word-break: break-all;\n  white-space: pre-wrap;\n  padding: 10px;\n  border-radius: 4px;\n}\n\n.severity {\n  width: 16px;\n  height: 16px;\n  border-radius: 16px;\n  margin-right: 6px\n}\n\nh1, h2, h3, h4, h5, h6 {\n  color: #333;\n  font-weight: bold;\n}\n\n.el-input__inner {\n  border-radius: 20px;\n}\n\n.el-input--suffix .el-input__inner {\n  padding-right: 10px;\n}\n\n.el-drawer__header {\n  margin-bottom: 10px !important;\n  border: none;\n}\n\n.el-drawer {\n  border-bottom-left-radius: 12px;\n  border-top-left-radius: 12px;\n  padding: 0 20px;\n  /*background-color: RGBA(247, 250, 255, 1);*/\n}\n\ntable {\n  max-width: 100%;\n  background-color: transparent;\n  border-collapse: collapse;\n  width: 100%;\n}\n\ntable th,\ntable td {\n  padding: 8px;\n  line-height: 1.5;\n  text-align: left;\n  vertical-align: top;\n  border: 1px solid #e1e1e1;\n}\n\ntable th {\n  font-weight: bold;\n}\n\ntable caption + thead tr:first-child th,\ntable caption + thead tr:first-child td,\ntable colgroup + thead tr:first-child th,\ntable colgroup + thead tr:first-child td,\ntable thead:first-child tr:first-child th,\ntable thead:first-child tr:first-child td {\n  border: 1px solid #e1e1e1;\n  background-color: #f5f5f5;\n}\n\n.el-carousel__button {\n  background-color: #999999 !important;\n}\n\n.el-carousel__container {\n  height: 260px!important;\n}\n\n</style>\n\n<style lang=\"scss\" scoped>\n\n.chat-container {\n  overflow-y: scroll;\n  background: RGBA(242, 246, 255, 1.00);\n  border-radius: 12px;\n  flex-shrink: 0\n}\n\n.fade-enter-active, .fade-leave-active {\n  transition: opacity 0.5s;\n}\n\n.review-step {\n  width: 100%;\n  height: calc(100vh - 120px);\n  flex-shrink: 0;\n}\n\n.container {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n}\n\n.diagnose-item {\n  flex-shrink: 0;\n  padding: 10px 0;\n  margin-bottom: 10px;\n  border-bottom-left-radius: 10px;\n  border-top-left-radius: 10px;\n  transition: height 3s ease-in-out;\n  .title {\n    color: #333333;\n    font-size: 16px;\n  }\n}\n\n.breathing-box {\n  box-shadow: 0 0 5px 5px RGBA(103, 194, 58, 0.5);\n  animation: breathing 1.8s infinite alternate;\n}\n\n@keyframes breathing {\n  0% {\n    box-shadow: 0 5px 5px RGBA(103, 194, 58, 0.2);\n  }\n  100% {\n    box-shadow: 0 0 5px 5px RGBA(103, 194, 58, 0.5);\n  }\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ReportComponent.vue?vue&type=template&id=6f4f86a6&scoped=true&\"\nimport script from \"./ReportComponent.vue?vue&type=script&lang=js&\"\nexport * from \"./ReportComponent.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ReportComponent.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./ReportComponent.vue?vue&type=style&index=1&id=6f4f86a6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f4f86a6\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[(_vm.componentError != '')?_c('div',[_c('h1',{staticClass:\"err__title\"},[_vm._v(\"Component Error\")]),_c('div',{staticClass:\"err__msg\"},[_vm._v(_vm._s(_vm.componentError))])]):(_vm.renderData != undefined)?_vm._t(\"default\",null,{\"args\":_vm.renderData.args,\"disabled\":_vm.renderData.disabled}):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nimport Vue from 'vue';\nimport { RenderData, Streamlit } from \"streamlit-component-lib\";\n\nexport default Vue.extend({\n  name: \"withStreamlitConnection\",\n  data: () => ({\n    renderData: (undefined as unknown) as RenderData,\n    componentError: \"\"\n  }),\n  methods: {\n    /**\n     * Streamlit is telling this component to redraw.\n     * We save the render data in component's data.\n     */\n    onRenderEvent: function(event: Event): void {\n      const renderEvent = event as CustomEvent<RenderData>;\n      this.renderData = renderEvent.detail; \n      this.componentError = \"\"\n    }\n  },\n  mounted(): void {\n    // Set up event listeners, and signal to Streamlit that we're ready.\n    // We won't render the component until we receive the first RENDER_EVENT.\n    Streamlit.events.addEventListener(\n      Streamlit.RENDER_EVENT,\n      this.onRenderEvent\n    );\n    Streamlit.setComponentReady();\n    Streamlit.setFrameHeight();\n  },\n  updated(): void {\n    Streamlit.setFrameHeight()\n  },\n  destroyed(): void {\n    Streamlit.events.removeEventListener(\n      Streamlit.RENDER_EVENT,\n      this.onRenderEvent\n    );\n  },\n  errorCaptured(err: Error): void {\n    this.componentError = String(err);\n  }\n})\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--14-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/ts-loader/index.js??ref--14-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--14-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/ts-loader/index.js??ref--14-3!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"", "import { render, staticRenderFns } from \"./WithStreamlitConnection.vue?vue&type=template&id=17a60eb2&scoped=true&\"\nimport script from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"\nexport * from \"./WithStreamlitConnection.vue?vue&type=script&lang=ts&\"\nimport style0 from \"./WithStreamlitConnection.vue?vue&type=style&index=0&id=17a60eb2&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17a60eb2\",\n  null\n  \n)\n\nexport default component.exports", "<!--\n  We bootstrap our Component to Streamlit with our scoped slot in the top-level App.\n  This is where scoped slot passes Streamlit ;`args` data from itself to children MyComponent.\n  You should not have to edit this, but are free to do so :)\n-->\n<template>\n  <div id=\"app\">\n    <WithStreamlitConnection v-slot=\"{ args }\">\n      <ReportComponent :args=\"args.args || {}\" />\n    </WithStreamlitConnection>\n  </div>\n</template>\n\n<script>\nimport ReportComponent from \"./ReportComponent\";\n\n// \"withStreamlitConnection\" is a scoped slot. It bootstraps the\n// connection between your component and the Streamlit app, and handles\n// passing arguments from Python -> Component.\n//\n// You don't need to edit withStreamlitConnection (but you're welcome to!).\nimport WithStreamlitConnection from \"./streamlit/WithStreamlitConnection.vue\";\n\nexport default {\n  name: \"App\",\n  components: { ReportComponent, WithStreamlitConnection }\n};\n</script>\n\n<style>\n\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=1bb0bc9c&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export default {\n  analysisButton: '分析',\n  timeRangeTip: '请选择异常发生时间进行分析',\n  timeTip: '选择异常发生时间',\n  refreshTip: '自动刷新',\n  queryTimeTip: '异常时间',\n  modelTip: '诊断模型',\n  instanceTip: '实例',\n  reviewDrawerTitle: '回看分析过程',\n  timeStartTip: '开始时间',\n  timeEndTip: '结束时间',\n  playbackButton: '回放',\n  reportButton: '报告',\n  setpTitle1: '异常分析',\n  playbackAnimationTip: '回放动画',\n  animationSpeedTip: '动画速度',\n  setpTip1: 'DBA收到异常提醒后，会针对该异常进行分析，进而分配任务给不同的同事，接收到任务的同事会先独立进行分析。',\n  setpTitle2: '圆桌讨论',\n  setpTip2: '接收任务的同事独立进行异常分析后，会加入群组，进行圆桌讨论。',\n  setpTitle3: '报告展示',\n  setpTip3: '圆桌讨论后，DBA会将讨论结果汇总，出具异常分析诊断报告。',\n  reportDrawerTitle: '分析报告',\n  timeRangeSelectTip: '请选择异常发生时间',\n  modelUsePrefixTip: '诊断报告由',\n  modelUseSuffixTip: '生成'\n}\n", "export default {\n  analysisButton: 'Analysis',\n  timeRangeTip: 'Select the time when the exception occurs for analysis',\n  timeTip: 'Select Anomaly Time',\n  refreshTip: 'Auto Refresh',\n  queryTimeTip: 'Anomaly Time',\n  modelTip: 'Anomaly Model',\n  instanceTip: 'Instance',\n  reviewDrawerTitle: 'Analysis Process Review',\n  timeStartTip: 'Start Time',\n  timeEndTip: 'Start Time',\n  reportDrawerTitle: 'Analysis Report',\n  playbackButton: 'Playback',\n  playbackAnimationTip: 'Playback Animation',\n  animationSpeedTip: 'Animation Speed',\n  reportButton: 'Report',\n  setpTitle1: 'Anomaly Analysis',\n  setpTip1: 'After receiving an exception notification, the DBA analyzes the exception and assigns tasks to different colleagues. The colleagues who receive the task perform the analysis independently first.',\n  setpTitle2: 'Round Table Discussion',\n  setpTip2: 'After the colleagues receiving the task independently analyze the anomaly, they join the group for a roundtable discussion.',\n  setpTitle3: 'Presentation Of Report',\n  setpTip3: 'After the roundtable discussion, the DBA will summarize the discussion results and issue an exception analysis and diagnosis report.',\n  timeRangeSelectTip: 'Select the time period when an anomaly occurs',\n  modelUsePrefixTip: 'Reports are generated by ',\n  modelUseSuffixTip: ''\n}\n", "import Vue from 'vue'\nimport VueI18n from 'vue-i18n'\nimport zh from './zh'\nimport en from './en'\nVue.use(VueI18n) // 全局注册国际化包\n\n// 准备翻译的语言环境信息\nconst i18n = new VueI18n({\n  locale: localStorage.getItem('LanguageSwitching') || 'zh', // 初始化中文\n  messages: {\n    'zh': zh,\n    'en': en\n  }\n})\n\nexport default i18n\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport '@/styles/index.scss' // global css\nimport 'element-ui/lib/theme-chalk/reset.css'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport '@/styles/vue-hljs-theme.css'\nimport '@/styles/common.css'\nimport i18n from './lang/index'\nVue.use(ElementUI)\nVue.config.productionTip = false\n\nnew Vue({\n  i18n,\n  render: h => h(App),\n}).$mount('#app')\n", "module.exports = __webpack_public_path__ + \"img/configuration_robot.7b743a08.webp\";", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReportComponent.vue?vue&type=style&index=0&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/dba_robot.f875048d.webp\";"], "sourceRoot": ""}